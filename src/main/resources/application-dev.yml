spring:
  servlet:
    multipart:
      max-file-size: 100MB
  redis:
    host: *************
    port: 6379
    password: redis_QQZwRz
    database: 0
  main:
    allow-bean-definition-overriding: true
  application:
    name: wiimoo-admin
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: jdbc:mysql://*************:3306/wiimoo?useUnicode=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai
    username: wiimoo
    password: fsJErweRSXT3bwp6
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      # 连接池核心参数
      initial-size: 5
      min-idle: 5
      max-active: 50
      max-wait: 60000

      # 连接有效性检测
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      # 连接泄露检测
      remove-abandoned: true
      remove-abandoned-timeout: 300
      log-abandoned: true
    # 防止SQL注入
    filter:
      wall:
        config:
          multi-statement-allow: false # 禁止批量执行
          none-base-statement-allow: false


server:
  port: 8080
  servlet:
    context-path: /wiimoo
    encoding:
      charset: UTF-8
      force: true

logging:
  config: classpath:logback-spring.xml
  level:
    root: info
    com: info
  file:
    name: ${spring.application.name}
    path: ./logs

storeBaseUrl: http://localhost:9000
alipayNotifyUrl: http://localhost:8080/alipay/notify
wechatNotifyUrl: http://localhost:8080/wechat/notify

task:
  order:
    enabled: false
  pay:
    enabled: false