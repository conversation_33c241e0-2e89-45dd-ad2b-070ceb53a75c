spring:
  servlet:
    multipart:
      max-file-size: 100MB
  redis:
    host: *************
    port: 6379
    password: redis_QQZwRz
    database: 0
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
      USE_BIG_DECIMAL_FOR_FLOATS: true
      # null字段不返回前端
    default-property-inclusion: non_null
    parser:
      ALLOW_UNQUOTED_FIELD_NAMES: true
  main:
    allow-bean-definition-overriding: true
  application:
    name: wiimoo-admin
  datasource:
    url: ***********************************************************************************************************
    username: wiimoo
    password: fsJErweRSXT3bwp6
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-test-query: SELECT 1
      connection-timeout: 30000
      validation-timeout: 5000
      idle-timeout: 600000  # 10 分钟空闲后回收
      max-lifetime:  25200000  # 7 小时（单位：毫秒，7*60*60*1000）
      leak-detection-threshold: 5000  # 毫秒，检测连接泄漏


server:
  port: 80
  servlet:
    context-path: /wiimoo
    encoding:
      charset: UTF-8
      force: true

logging:
  config: classpath:logback-spring.xml
  level:
    root: info
    com: info
  file:
    name: ${spring.application.name}
    path: ./logs

storeBaseUrl: https://store-uat.wiimoo.cn
alipayNotifyUrl: https://api-uat.wiimoo.cn/alipay/notify
wechatNotifyUrl: https://api-uat.wiimoo.cn/wechat/notify

task:
  order:
    enabled: true
  pay:
    enabled: true