package com.xingkong.wiimoo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class ApplicationBoot {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationBoot.class);

    public static void main(String[] args) {
        SpringApplication.run(ApplicationBoot.class, args);
        LOGGER.info("wiimoo 后台管理系统启动成功！");
    }

}