package com.xingkong.wiimoo.client.autoreplace.db.entity;

import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;

@Table(value = "work_queue")
public class WorkQueueEntity {
    private String id;
    private String name;
    private String workData;
    private String workConfig;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWorkData() {
        return workData;
    }

    public void setWorkData(String workData) {
        this.workData = workData;
    }

    public String getWorkConfig() {
        return workConfig;
    }

    public void setWorkConfig(String workConfig) {
        this.workConfig = workConfig;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}