package com.xingkong.wiimoo.common.auth.service;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.common.auth.oauth2.OAuth2Service;
import com.xingkong.wiimoo.common.auth.oauth2.logto.LogtoConfig;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthTokenResponse;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthUserInfoResponse;
import com.xingkong.wiimoo.common.auth.web.dto.AuthorizationCodeLoginRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlResponse;
import com.xingkong.wiimoo.common.auth.web.dto.LoginResponseData;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerEntity;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerScoreEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.time.LocalDateTime;

import static com.xingkong.wiimoo.server.consumer.db.entity.table.ConsumerTableDef.Consumer;

@Service
@Validated
public class LoginStoreService {

    @Autowired
    private OAuth2Service oAuth2Service;

    public Result<GetAuthUrlResponse> getAuthUrl(@Valid GetAuthUrlRequest getAuthUrlRequest) {
        Result<String> authUrl = oAuth2Service.getOAuthUrl(LogtoConfig.WIIMOO_STORE.getClientId(), getAuthUrlRequest.getRedirectUri(), LogtoConfig.Auth_Endpoint,getAuthUrlRequest.getState());
        if (!authUrl.getResultCode().equals(ResultCode.SUCCESS.getCode())) {
            return Result.simpleFail(authUrl);
        }
        GetAuthUrlResponse getAuthUrlResponse = new GetAuthUrlResponse();
        getAuthUrlResponse.setAuthUrl(authUrl.getData());
        return Result.success(getAuthUrlResponse);
    }

    public Result<LoginResponseData> authorizationCodeLoginStore(@Valid AuthorizationCodeLoginRequest authorizationCodeLoginRequest) {

        Result<GetOAuthTokenResponse> getOAuthTokenResponseResult = oAuth2Service.getTokenByCode(LogtoConfig.WIIMOO_STORE.getClientId(), LogtoConfig.WIIMOO_STORE.getClientSecret(), LogtoConfig.Token_Endpoint, authorizationCodeLoginRequest.getRedirectUri());
        if (!getOAuthTokenResponseResult.getResultCode().equals(ResultCode.SUCCESS.getCode())) {
            return Result.simpleFail(getOAuthTokenResponseResult);
        }
        Result<GetOAuthUserInfoResponse> getOAuthUserInfoResponseResult = oAuth2Service.getUserInfo(getOAuthTokenResponseResult.getData().getAccess_token(), LogtoConfig.Userinfo_Endpoint);
        if (!getOAuthUserInfoResponseResult.getResultCode().equals(ResultCode.SUCCESS.getCode())) {
            return Result.simpleFail(getOAuthUserInfoResponseResult);
        }

        GetOAuthUserInfoResponse oAuthUserInfo = getOAuthUserInfoResponseResult.getData();
        ConsumerEntity consumerEntity = QueryChain.of(ConsumerEntity.class).select()
                .from(ConsumerEntity.class)
                .where(Consumer.Id.eq(oAuthUserInfo.getSub()))
                .one();
        if (consumerEntity == null) {
            consumerEntity = new ConsumerEntity();
            consumerEntity.setId(oAuthUserInfo.getSub());
            consumerEntity.setCreatedTime(LocalDateTime.now());
            DbChain.table(ConsumerEntity.class).save(consumerEntity);
            ConsumerScoreEntity consumerScoreEntity = new ConsumerScoreEntity();
            consumerScoreEntity.setId(UlidCreator.getUlid().toString());
            consumerScoreEntity.setConsumerId(consumerEntity.getId());
            consumerScoreEntity.setScore(0);
            consumerScoreEntity.setCreatedTime(LocalDateTime.now());
            consumerScoreEntity.setUpdatedTime(consumerScoreEntity.getCreatedTime());
            DbChain.table(ConsumerScoreEntity.class).save(consumerScoreEntity);
        }
        StpKit.STORE.login(consumerEntity.getId());
        LoginResponseData loginResponseData = buildLoginResponseData(StpKit.STORE.getTokenInfo(), oAuthUserInfo);
        loginResponseData.setEmail(oAuthUserInfo.getEmail());
        return Result.success(loginResponseData);
    }

    private LoginResponseData buildLoginResponseData(SaTokenInfo saTokenInfo, String userId) {

        GetOAuthUserInfoResponse oAuthUserInfo = new GetOAuthUserInfoResponse();
        oAuthUserInfo.setSub(userId);

        return buildLoginResponseData(saTokenInfo, oAuthUserInfo);
    }

    private LoginResponseData buildLoginResponseData(SaTokenInfo saTokenInfo, GetOAuthUserInfoResponse oAuthUserInfo) {
        LoginResponseData loginResponseData = new LoginResponseData();
        loginResponseData.setUserId(oAuthUserInfo.getSub());
        loginResponseData.setNickName(oAuthUserInfo.getName());
        if (StringUtils.isEmpty(oAuthUserInfo.getName())) {
            loginResponseData.setNickName(oAuthUserInfo.getUsername());
        }
        loginResponseData.setLoginName(oAuthUserInfo.getUsername());
        loginResponseData.setPhone(oAuthUserInfo.getPhone_number());
        loginResponseData.setEmail(oAuthUserInfo.getEmail());
        loginResponseData.setTokenValue(saTokenInfo.getTokenValue());
        loginResponseData.setTokenName(saTokenInfo.getTokenName());
        loginResponseData.setExpiresIn(LocalDateTime.now().plusSeconds(saTokenInfo.getTokenTimeout()));
        return loginResponseData;
    }

    public Result<LoginResponseData> getTokenInfo() {
        LoginResponseData loginResponseData = buildLoginResponseData(StpKit.STORE.getTokenInfo(), StpKit.STORE.getLoginIdAsString());
        return Result.success(loginResponseData);
    }

    public Result<String> logout(String redirectUrl) {
        StpKit.getStpLogic(StpKit.STORE_TYPE).logout();
        return Result.success(oAuth2Service.getLogoutUrl(LogtoConfig.End_Session_Endpoint, LogtoConfig.WIIMOO_STORE.getClientId(), redirectUrl));
    }

}