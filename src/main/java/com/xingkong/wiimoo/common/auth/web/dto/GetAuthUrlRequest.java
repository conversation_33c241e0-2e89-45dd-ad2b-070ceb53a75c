package com.xingkong.wiimoo.common.auth.web.dto;

import io.swagger.annotations.ApiModel;

@ApiModel(description = "获取授权地址请求对象")
public class GetAuthUrlRequest {
    private String redirectUri;
    private String state;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }
}