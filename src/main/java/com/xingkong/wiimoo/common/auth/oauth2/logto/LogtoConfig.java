package com.xingkong.wiimoo.common.auth.oauth2.logto;


import java.util.HashMap;
import java.util.Map;

public enum LogtoConfig {

    WIIMOO_ADMIN("rnh9xo9aw7412tnh0zxvp", "OpPa0w6HMmLO5tF5nHxU4GQBYaAohqMS"),
    WIIMOO_STORE("6v4pjh1q4a91m8czjr9n0", "CBxNHhXnxkVJWMBELVGNDBm2C0kUHAHK");

    LogtoConfig(String clientId, String clientSecret) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
    }

    private static final String Logto_Endpoint = "https://auth.wiimoo.cn";
    public static final String Auth_Endpoint = Logto_Endpoint + "/oidc/auth";
    public static final String Token_Endpoint = Logto_Endpoint + "/oidc/token";
    public static final String Userinfo_Endpoint = Logto_Endpoint + "/oidc/me";
    public static final String End_Session_Endpoint = Logto_Endpoint + "/oidc/session/end";

    private String clientId;
    private String clientSecret;


    public String getClientId() {
        return clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }


    private static final Map<String, LogtoConfig> LOGTO_CONFIG_MAP = new HashMap<>();

    static {
        for (LogtoConfig config : LogtoConfig.values()) {
            LOGTO_CONFIG_MAP.put(config.getClientId(), config);
        }
    }

    public static LogtoConfig getLogtoConfig(String clientId) {
        return LOGTO_CONFIG_MAP.get(clientId);
    }
}