package com.xingkong.wiimoo.common.auth.service;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.common.auth.entity.StaffEntity;
import com.xingkong.wiimoo.common.auth.oauth2.OAuth2Service;
import com.xingkong.wiimoo.common.auth.oauth2.logto.LogtoConfig;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthTokenResponse;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthUserInfoResponse;
import com.xingkong.wiimoo.common.auth.web.dto.AuthorizationCodeLoginRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlResponse;
import com.xingkong.wiimoo.common.auth.web.dto.LoginResponseData;
import com.xingkong.wiimoo.common.config.StpKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.time.LocalDateTime;

import static com.xingkong.wiimoo.common.auth.entity.table.StaffTableDef.Staff;

@Service
@Validated
public class LoginAdminService {

    @Autowired
    private OAuth2Service oAuth2Service;

    public Result<GetAuthUrlResponse> getAuthUrl(@Valid GetAuthUrlRequest getAuthUrlRequest) {
        Result<String> authUrl = oAuth2Service.getOAuthUrl(LogtoConfig.WIIMOO_ADMIN.getClientId(), getAuthUrlRequest.getRedirectUri(), LogtoConfig.Auth_Endpoint,getAuthUrlRequest.getState());
        if (!authUrl.getResultCode().equals(ResultCode.SUCCESS.getCode())) {
            return Result.simpleFail(authUrl);
        }
        GetAuthUrlResponse getAuthUrlResponse = new GetAuthUrlResponse();
        getAuthUrlResponse.setAuthUrl(authUrl.getData());
        return Result.success(getAuthUrlResponse);
    }

    public Result<LoginResponseData> authorizationCodeLoginAdmin(@Valid AuthorizationCodeLoginRequest authorizationCodeLoginRequest) {
        Result<GetOAuthTokenResponse> getOAuthTokenResponseResult = oAuth2Service.getTokenByCode(LogtoConfig.WIIMOO_ADMIN.getClientId(), LogtoConfig.WIIMOO_ADMIN.getClientSecret(), LogtoConfig.Token_Endpoint, authorizationCodeLoginRequest.getRedirectUri());
        if (!getOAuthTokenResponseResult.getResultCode().equals(ResultCode.SUCCESS.getCode())) {
            return Result.simpleFail(getOAuthTokenResponseResult);
        }
        Result<GetOAuthUserInfoResponse> getOAuthUserInfoResponseResult = oAuth2Service.getUserInfo(getOAuthTokenResponseResult.getData().getAccess_token(), LogtoConfig.Userinfo_Endpoint);
        if (!getOAuthUserInfoResponseResult.getResultCode().equals(ResultCode.SUCCESS.getCode())) {
            return Result.simpleFail(getOAuthUserInfoResponseResult);
        }

        GetOAuthUserInfoResponse oAuthUserInfo = getOAuthUserInfoResponseResult.getData();
        StaffEntity staffEntity = QueryChain.of(StaffEntity.class).select().from(StaffEntity.class).where(Staff.Id.eq(oAuthUserInfo.getSub())).one();
        if (staffEntity == null) {
            return Result.businessError("用户没有权限");
        }
        StpKit.ADMIN.login(staffEntity.getId());
        LoginResponseData loginResponseData = buildLoginResponseData(StpKit.ADMIN.getTokenInfo(), oAuthUserInfo);
        return Result.success(loginResponseData);
    }

    private LoginResponseData buildLoginResponseData(SaTokenInfo saTokenInfo, String userId) {

        GetOAuthUserInfoResponse oAuthUserInfo = new GetOAuthUserInfoResponse();
        oAuthUserInfo.setSub(userId);

        return buildLoginResponseData(saTokenInfo, oAuthUserInfo);
    }

    private LoginResponseData buildLoginResponseData(SaTokenInfo saTokenInfo, GetOAuthUserInfoResponse oAuthUserInfo) {
        LoginResponseData loginResponseData = new LoginResponseData();
        loginResponseData.setUserId(oAuthUserInfo.getSub());
        loginResponseData.setNickName(oAuthUserInfo.getName());
        if (StringUtils.isEmpty(oAuthUserInfo.getName())) {
            loginResponseData.setNickName(oAuthUserInfo.getUsername());
        }
        loginResponseData.setLoginName(oAuthUserInfo.getUsername());
        loginResponseData.setPhone(oAuthUserInfo.getPhone_number());
        loginResponseData.setEmail(oAuthUserInfo.getEmail());
        loginResponseData.setTokenValue(saTokenInfo.getTokenValue());
        loginResponseData.setTokenName(saTokenInfo.getTokenName());
        loginResponseData.setExpiresIn(LocalDateTime.now().plusSeconds(saTokenInfo.getTokenTimeout()));
        return loginResponseData;
    }

    public Result<LoginResponseData> getTokenInfo() {
        LoginResponseData loginResponseData = buildLoginResponseData(StpKit.STORE.getTokenInfo(), StpKit.ADMIN.getLoginIdAsString());
        return Result.success(loginResponseData);
    }

    public Result<String> logout(String redirectUrl) {
        StpKit.getStpLogic(StpKit.ADMIN_TYPE).logout();
        return Result.success(oAuth2Service.getLogoutUrl(LogtoConfig.End_Session_Endpoint, LogtoConfig.WIIMOO_ADMIN.getClientId(), redirectUrl));
    }
}