package com.xingkong.wiimoo.common.auth.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.auth.service.LoginAdminService;
import com.xingkong.wiimoo.common.auth.web.dto.AuthorizationCodeLoginRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlResponse;
import com.xingkong.wiimoo.common.auth.web.dto.LoginResponseData;
import com.xingkong.wiimoo.common.config.StpKit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2024/5/20 9:23
 **/
@Api(tags = "认证服务接口")
@RestController
@RequestMapping("/admin/auth")
public class AdminAuthController {

    @Autowired
    private LoginAdminService loginAdminService;

    @ApiOperation(value = "获取登录鉴权url接口", notes = "获取登录鉴权url")
    @PostMapping("/get/url")
    @ResponseBody
    public Result<GetAuthUrlResponse> getAdminAuthUrl(@RequestBody GetAuthUrlRequest getAuthUrlRequest) {
        return loginAdminService.getAuthUrl(getAuthUrlRequest);
    }

    @ApiOperation(value = "授权码登录接口", notes = "授权码登录")
    @PostMapping("/login/by/authorizationCode")
    @ResponseBody
    public Result<LoginResponseData> authorizationCodeLoginAdmin(@RequestBody AuthorizationCodeLoginRequest authorizationCodeLoginRequest) {
        return loginAdminService.authorizationCodeLoginAdmin(authorizationCodeLoginRequest);
    }


    @ApiOperation(value = "获取token信息接口", notes = "获取当前登录用户的token信息")
    @GetMapping("/get/token")
    @ResponseBody
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    public Result<LoginResponseData> getTokenInfo() {
        return loginAdminService.getTokenInfo();
    }

    @ApiOperation(value = "退出登录接口", notes = "退出登录")
    @GetMapping("/logout")
    @ResponseBody
    public Result logout(
            @ApiParam(value = "退出后重定向地址", required = false)
            @RequestParam(value = "redirectUrl", required = false) String redirectUrl) {

        return loginAdminService.logout(redirectUrl);
    }
}