package com.xingkong.wiimoo.common.auth.service;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpLogic;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthUserInfoResponse;
import com.xingkong.wiimoo.common.auth.web.dto.GetWebAuthUrlRequest;
import com.xingkong.wiimoo.common.auth.web.dto.LoginResponseData;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.application.db.entity.ApplicationEntity;
import com.xingkong.wiimoo.server.application.service.ApplicationValidatedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Service
@Validated
public class LoginApplicationService {

    @Value("${storeBaseUrl}")
    private String storeBaseUrl;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ApplicationValidatedService applicationValidatedService;

    public Result<String> getLoginUrl(@Valid GetWebAuthUrlRequest getWebAuthUrlRequest) {
        return Result.success(storeBaseUrl + "/loginApplication?applicationCode=" + getWebAuthUrlRequest.getApplicationCode() + "&tempAuthCode=" + getWebAuthUrlRequest.getTempAuthCode());
    }

    public Result<LoginResponseData> getClientToken(@Valid @NotBlank(message = "临时认证码不能为空") String tempAuthCode) {
        LoginResponseData loginResponseData = (LoginResponseData) redisTemplate.opsForValue().get("tempAuthCode:" + tempAuthCode);
        if (loginResponseData == null) {
            return Result.businessError("登录信息不存在，请重新获取登录链接");
        }
        redisTemplate.delete("tempAuthCode:" + tempAuthCode);
        return Result.success(loginResponseData);
    }

    public Result loginApplication(@Valid @NotBlank(message = "应用编码不能为空") String applicationCode, @NotBlank(message = "临时认证码不能为空") String tempAuthCode) {
        Result<ApplicationEntity> applicationValidatedResult = applicationValidatedService.isValid(applicationCode);
        if (!ResultCode.SUCCESS.getCode().equals(applicationValidatedResult.getResultCode())) {
            return Result.simpleFail(applicationValidatedResult);
        }

        //登录实际应用
        StpLogic stpLogic = StpKit.getStpLogic(applicationValidatedResult.getData().getCode());
        String consumerId = StpKit.STORE.getLoginIdAsString();
        stpLogic.login(consumerId);

        SaTokenInfo appSatakenInfo = stpLogic.getTokenInfo();
        LoginResponseData loginResponseData = buildLoginResponseData(appSatakenInfo, consumerId);
        redisTemplate.opsForValue().set("tempAuthCode:" + tempAuthCode, loginResponseData);
        return Result.success();
    }

    private LoginResponseData buildLoginResponseData(SaTokenInfo saTokenInfo, String userId) {

        GetOAuthUserInfoResponse oAuthUserInfo = new GetOAuthUserInfoResponse();
        oAuthUserInfo.setSub(userId);

        return buildLoginResponseData(saTokenInfo, oAuthUserInfo);
    }

    private LoginResponseData buildLoginResponseData(SaTokenInfo saTokenInfo, GetOAuthUserInfoResponse oAuthUserInfo) {
        LoginResponseData loginResponseData = new LoginResponseData();
        loginResponseData.setUserId(oAuthUserInfo.getSub());
        loginResponseData.setNickName(oAuthUserInfo.getName());
        if (StringUtils.isEmpty(oAuthUserInfo.getName())) {
            loginResponseData.setNickName(oAuthUserInfo.getUsername());
        }
        loginResponseData.setLoginName(oAuthUserInfo.getUsername());
        loginResponseData.setPhone(oAuthUserInfo.getPhone_number());
        loginResponseData.setEmail(oAuthUserInfo.getEmail());
        loginResponseData.setTokenValue(saTokenInfo.getTokenValue());
        loginResponseData.setTokenName(saTokenInfo.getTokenName());
        loginResponseData.setExpiresIn(LocalDateTime.now().plusSeconds(saTokenInfo.getTokenTimeout()));
        return loginResponseData;
    }

    public Result<String> logout(@Valid @NotBlank(message = "应用编码不能为空") String applicationCode, String redirectUrl) {
        StpKit.getStpLogic(applicationCode).logout();
        return Result.success();
    }
}