package com.xingkong.wiimoo.common.auth.oauth2;

import com.dtflys.forest.annotation.*;
import com.dtflys.forest.http.ForestResponse;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthTokenRequest;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthTokenResponse;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthUserInfoResponse;

@BaseRequest(baseURL = "https://jpjaun.logto.app")
public interface OAuth2Api {
    @Post(url = "{tokenEndpoint}")
    ForestResponse<GetOAuthTokenResponse> getTokenByCode(@Var("tokenEndpoint")String tokenEndpoint, @JSONBody GetOAuthTokenRequest getOAuthTokenRequest);

    @Post(url = "{userInfoEndpoint}")
    ForestResponse<GetOAuthUserInfoResponse> getUserInfo(@Var("userInfoEndpoint")String userInfoEndpoint, @Header("Authorization") String accessToken);

    @Post(url = "{logoutEndpoint}")
    ForestResponse<Void> logout(@Var("logoutEndpoint")String logoutEndpoint, @Header("Authorization") String accessToken);
}