package com.xingkong.wiimoo.common.auth.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "获取登录地址请求对象")
public class GetWebAuthUrlRequest {
    @ApiModelProperty(value = "临时认证码，全局唯一，推荐使用uuid，用于获取实际用户的token", required = true)
    @NotBlank(message = "临时认证码不能为空")
    private String tempAuthCode;
    @ApiModelProperty(value = "应用id", required = true)
    @NotBlank(message = "应用id不能为空")
    private String applicationCode;

    public String getTempAuthCode() {
        return tempAuthCode;
    }

    public void setTempAuthCode(String tempAuthCode) {
        this.tempAuthCode = tempAuthCode;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }
}