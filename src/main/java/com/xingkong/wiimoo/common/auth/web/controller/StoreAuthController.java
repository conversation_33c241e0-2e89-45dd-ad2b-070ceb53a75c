package com.xingkong.wiimoo.common.auth.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.auth.service.LoginStoreService;
import com.xingkong.wiimoo.common.auth.web.dto.AuthorizationCodeLoginRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlRequest;
import com.xingkong.wiimoo.common.auth.web.dto.GetAuthUrlResponse;
import com.xingkong.wiimoo.common.auth.web.dto.LoginResponseData;
import com.xingkong.wiimoo.common.config.StpKit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2024/5/20 9:23
 **/
@Api(tags = "store认证服务接口")
@RestController
@RequestMapping("/store/auth")
public class StoreAuthController {

    @Autowired
    private LoginStoreService loginStoreService;

    @ApiOperation(value = "获取登录鉴权url", notes = "获取登录鉴权url")
    @PostMapping("/get/url")
    @ResponseBody
    public Result<GetAuthUrlResponse> getStoreAuthUrl(@RequestBody GetAuthUrlRequest getAuthUrlRequest) {
        return loginStoreService.getAuthUrl(getAuthUrlRequest);
    }

    @ApiOperation(value = "授权码登录接口", notes = "授权码登录")
    @PostMapping("/login/by/authorizationCode")
    @ResponseBody
    public Result<LoginResponseData> authorizationCodeLoginStore(@RequestBody AuthorizationCodeLoginRequest authorizationCodeLoginRequest) {
        return loginStoreService.authorizationCodeLoginStore(authorizationCodeLoginRequest);
    }

    @ApiOperation(value = "获取token信息接口", notes = "获取当前登录用户的token信息")
    @GetMapping("/get/token")
    @ResponseBody
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    public Result<LoginResponseData> getTokenInfo() {
        return loginStoreService.getTokenInfo();
    }

    @ApiOperation(value = "退出登录接口", notes = "退出登录")
    @GetMapping("/logout")
    @ResponseBody
    public Result logout(
            @ApiParam(value = "退出后重定向地址", required = false)
            @RequestParam(value = "redirectUrl", required = false) String redirectUrl) {

        return loginStoreService.logout(redirectUrl);
    }
}