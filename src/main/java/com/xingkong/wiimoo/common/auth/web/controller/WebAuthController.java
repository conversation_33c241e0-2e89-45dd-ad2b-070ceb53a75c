package com.xingkong.wiimoo.common.auth.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.auth.service.LoginApplicationService;
import com.xingkong.wiimoo.common.auth.web.dto.GetWebAuthUrlRequest;
import com.xingkong.wiimoo.common.auth.web.dto.LoginResponseData;
import com.xingkong.wiimoo.common.config.StpKit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/5/20 9:23
 **/
@Api(tags = "web认证服务接口")
@RestController
@RequestMapping("/web/auth")
public class WebAuthController {

    @Autowired
    private LoginApplicationService loginApplicationService;

    @ApiOperation(value = "获取web授权登录地址接口", notes = "客户端调用该接口，获取web授权登录地址，然后拉起浏览器，跳转到该地址，用户授权登录后，通过临时认证码获取token")
    @PostMapping("/get/url")
    @ResponseBody
    public Result<String> getLoginUrl(@RequestBody GetWebAuthUrlRequest getWebAuthUrlRequest) {
        return loginApplicationService.getLoginUrl(getWebAuthUrlRequest);
    }

    @ApiOperation(value = "获取用户token接口", notes = "通过临时认证码获取用户token")
    @GetMapping("/get/token")
    @ResponseBody
    public Result<LoginResponseData> getClientToken(@ApiParam(value = "临时认证码", required = true) @RequestParam("tempAuthCode") String tempAuthCode) {
        return loginApplicationService.getClientToken(tempAuthCode);
    }

    @ApiOperation(value = "登录指定应用接口", notes = "web端登录成功后，通过临时认证码登录指定应用")
    @GetMapping("/login/{applicationCode}")
    @ResponseBody
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    public Result loginApplication(@ApiParam(value = "应用编码", required = true) @PathVariable("applicationCode") String applicationCode,
                                   @ApiParam(value = "临时认证码", required = true) @RequestParam("tempAuthCode") String tempAuthCode) {
        return loginApplicationService.loginApplication(applicationCode, tempAuthCode);
    }

    @ApiOperation(value = "退出登录接口", notes = "退出登录")
    @GetMapping("/logout/{applicationCode}")
    @ResponseBody
    public Result logout(@ApiParam(value = "应用编码", required = true)
                         @NotBlank(message = "应用编码不能为空")
                         @PathVariable("applicationCode") String applicationCode,
                         @ApiParam(value = "退出后重定向地址", required = false)
                         @RequestParam(value = "redirectUrl", required = false) String redirectUrl) {

        return loginApplicationService.logout(applicationCode,redirectUrl);
    }

}