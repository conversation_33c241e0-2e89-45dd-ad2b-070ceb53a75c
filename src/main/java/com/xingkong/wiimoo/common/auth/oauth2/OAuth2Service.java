package com.xingkong.wiimoo.common.auth.oauth2;

import com.dtflys.forest.http.ForestResponse;
import com.github.f4b6a3.ulid.UlidCreator;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthTokenRequest;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthTokenResponse;
import com.xingkong.wiimoo.common.auth.oauth2.webdto.GetOAuthUserInfoResponse;
import com.xingkong.wiimoo.common.domain.PhoneNumber;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;

@Service
public class OAuth2Service {

    @Resource
    private OAuth2Api oAuth2Api;

    public Result<String> getOAuthUrl(String clientId, String redirectUri, String authorizationEndpoint, String state) {
        UriComponents uriComponents = UriComponentsBuilder.fromUriString(authorizationEndpoint)
                .queryParam("client_id", clientId)
                .queryParam("response_type", "code")
                .queryParam("state", StringUtils.isEmpty(state) ? UlidCreator.getUlid().toString(): state)
                .queryParam("scope", "openid profile email phone")
                .queryParam("redirect_uri", redirectUri)
                .build();

        return Result.success(uriComponents.toUriString());
    }

    public Result<GetOAuthTokenResponse> getTokenByCode(String clientId, String clientSecret, String tokenEndpoint, String redirectUri) {
        UriComponents uriComponents = UriComponentsBuilder.fromUriString(redirectUri).build();
        MultiValueMap<String, String> params = uriComponents.getQueryParams();
        String code = params.getFirst("code");
        String loginRedirectUri = UriComponentsBuilder.fromUriString(redirectUri).replaceQuery(null).fragment(null).build().toUriString();

        GetOAuthTokenRequest getOAuthTokenRequest = new GetOAuthTokenRequest();
        getOAuthTokenRequest.setGrant_type("authorization_code");
        getOAuthTokenRequest.setCode(code);
        getOAuthTokenRequest.setClient_id(clientId);
        getOAuthTokenRequest.setClient_secret(clientSecret);


        getOAuthTokenRequest.setRedirect_uri(loginRedirectUri);
        ForestResponse<GetOAuthTokenResponse> getOAuthTokenResponse = oAuth2Api.getTokenByCode(tokenEndpoint, getOAuthTokenRequest);
        if (!getOAuthTokenResponse.isSuccess()) {
            return Result.authenticationError("获取token失败");
        }
        if (getOAuthTokenResponse.getResult().getError() != null) {
            return Result.authenticationError(getOAuthTokenResponse.getResult().getError());
        }
        return Result.success(getOAuthTokenResponse.getResult());
    }

    public Result<GetOAuthUserInfoResponse> getUserInfo(String accessToken, String userInfoEndpoint) {
        ForestResponse<GetOAuthUserInfoResponse> getUserInfoResponse = oAuth2Api.getUserInfo(userInfoEndpoint, "Bearer " + accessToken);
        if (!getUserInfoResponse.isSuccess()) {
            return Result.authenticationError("获取用户信息失败");
        }
        if (!StringUtils.isEmpty(getUserInfoResponse.getResult().getPhone_number())) {
            getUserInfoResponse.getResult().setPhone_number(PhoneNumber.fromString(getUserInfoResponse.getResult().getPhone_number()).getNationalNumber());
        }
        return Result.success(getUserInfoResponse.getResult());
    }

    public String getLogoutUrl(String logoutEndpoint, String client_id, String redirectUrl) {
        if (StringUtils.isEmpty(redirectUrl)) {
            return logoutEndpoint;
        }
        return logoutEndpoint + "?client_id=" + client_id + "&post_logout_redirect_uri=" + redirectUrl;
    }

}