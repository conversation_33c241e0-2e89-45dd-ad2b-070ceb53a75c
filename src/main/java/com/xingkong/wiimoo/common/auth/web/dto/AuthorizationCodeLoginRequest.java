package com.xingkong.wiimoo.common.auth.web.dto;

import com.xingkong.base.infrastructure.ability.validation.ConditionalValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "授权码登录请求对象")
@ConditionalValidation
public class AuthorizationCodeLoginRequest {
    @ApiModelProperty(value = "重定向地址，若获取登录url时传入了redirectUri，则必须传入，且必须与获取登录url时传入的redirectUri一致", required = false)
    private String redirectUri;

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }
}