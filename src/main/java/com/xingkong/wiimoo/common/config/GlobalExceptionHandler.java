package com.xingkong.wiimoo.common.config;

import cn.dev33.satoken.exception.SaTokenException;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.Result;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(SaTokenException.class)
    public Result handlerSaTokenException(SaTokenException e) {
        logger.error("SaTokenException", e);
        // 默认的提示
        return Result.authenticationError(e.getMessage());
    }
}