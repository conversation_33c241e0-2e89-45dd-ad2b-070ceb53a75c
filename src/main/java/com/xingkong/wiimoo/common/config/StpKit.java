package com.xingkong.wiimoo.common.config;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * StpLogic 门面类，管理项目中所有的 StpLogic 账号体系
 */
@Configuration
public class StpKit {

    /**
     * 默认原生会话对象
     */
    public static final StpLogic DEFAULT = StpUtil.stpLogic;

    /**
     * Admin 会话对象，管理 Admin 表所有账号的登录、权限认证
     */
    public static final String ADMIN_TYPE = "admin";
    public static final StpLogic ADMIN = new StpLogic(ADMIN_TYPE);
    @PostConstruct
    public void setAdminConfig (){
        SaTokenConfig config = new SaTokenConfig();
        config.setTokenName("satoken-"+ADMIN_TYPE);             // token名称 (同时也是cookie名称)
        config.setTimeout(7 * 24 * 60 * 60);       // token有效期，单位s 默认7天，
        config.setIsShare(false);                    // 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
        config.setTokenStyle("uuid");
        ADMIN.setConfig(config);
    }

    /**
     * User 会话对象，管理 User 表所有账号的登录、权限认证
     */
    public static final String STORE_TYPE = "store";
    public static final StpLogic STORE = new StpLogic(STORE_TYPE);
    @PostConstruct
    public void setStoreConfig () {
        SaTokenConfig config = new SaTokenConfig();
        config.setTokenName("satoken-"+STORE_TYPE);             // token名称 (同时也是cookie名称)
        config.setTimeout(7 * 24 * 60 * 60);       // token有效期，单位s 默认7天，
        config.setIsShare(false);                    // 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
        config.setTokenStyle("uuid");
        STORE.setConfig(config);
    }
    private static Map<String, StpLogic> stpLogicMap = new HashMap<>();
    public static StpLogic getStpLogic(String type) {
        if (ADMIN_TYPE.equals(type)) {
            return ADMIN;
        } else if (STORE_TYPE.equals(type)) {
            return STORE;
        } else {
            StpLogic stpLogic = stpLogicMap.get(type);
            if (stpLogic == null) {
                stpLogic = new StpLogic(type);
                SaTokenConfig config = new SaTokenConfig();
                config.setTokenName("satoken-"+type);             // token名称 (同时也是cookie名称)
                config.setTimeout(30 * 24 * 60 * 60);       // token有效期，单位s 默认30天
                config.setIsShare(false);                    // 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
                config.setTokenStyle("uuid");
                stpLogic.setConfig(config);
                stpLogicMap.put(type, stpLogic);
            }
            return stpLogic;
        }
    }

}