package com.xingkong.wiimoo.common.support;

import java.util.Map;

public enum ResourceStatusEnum {
    WAITING("WAITING","待审核"),
    ENABLE("ENABLE","启用"),
    DISABLE("DISABLE","停用");

    private String code;
    private String desc;

    ResourceStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, ResourceStatusEnum> map = new java.util.HashMap<String, ResourceStatusEnum>();
    static {
        for (ResourceStatusEnum statusOfDataDef : ResourceStatusEnum.values()) {
            map.put(statusOfDataDef.getCode(), statusOfDataDef);
        }
    }
    public static ResourceStatusEnum getStatusOfDataDef(String code) {
        return map.get(code);
    }
}