package com.xingkong.wiimoo.common.support;

import java.util.Map;

public enum VipStatusEnum {
    WAITING("WAITING","待审核"),
    ENABLE("ENABLE","启用"),
    DISABLE("DISABLE","停用");

    private String code;
    private String desc;

    VipStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, VipStatusEnum> map = new java.util.HashMap<String, VipStatusEnum>();
    static {
        for (VipStatusEnum statusOfDataDef : VipStatusEnum.values()) {
            map.put(statusOfDataDef.getCode(), statusOfDataDef);
        }
    }
    public static VipStatusEnum getStatusOfDataDef(String code) {
        return map.get(code);
    }
}