package com.xingkong.wiimoo.common.support;

import java.util.Map;

public enum ScorePriceStatusEnum {
    WAITING("WAITING","待审核"),
    ENABLE("ENABLE","启用"),
    DISABLE("DISABLE","停用");

    private String code;
    private String desc;

    ScorePriceStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, ScorePriceStatusEnum> map = new java.util.HashMap<String, ScorePriceStatusEnum>();
    static {
        for (ScorePriceStatusEnum statusOfDataDef : ScorePriceStatusEnum.values()) {
            map.put(statusOfDataDef.getCode(), statusOfDataDef);
        }
    }
    public static ScorePriceStatusEnum getStatusOfDataDef(String code) {
        return map.get(code);
    }
}