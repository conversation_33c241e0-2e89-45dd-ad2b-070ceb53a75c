package com.xingkong.wiimoo.common.support;

import java.util.Map;

public enum ApplicationStatusEnum {
    WAITING("WAITING","待审核"),
    ENABLE("ENABLE","启用"),
    DISABLE("DISABLE","停用");

    private String code;
    private String desc;

    ApplicationStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, ApplicationStatusEnum> map = new java.util.HashMap<String, ApplicationStatusEnum>();
    static {
        for (ApplicationStatusEnum statusOfDataDef : ApplicationStatusEnum.values()) {
            map.put(statusOfDataDef.getCode(), statusOfDataDef);
        }
    }
    public static ApplicationStatusEnum getStatusOfDataDef(String code) {
        return map.get(code);
    }
}