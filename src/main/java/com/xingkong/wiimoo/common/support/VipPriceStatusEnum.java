package com.xingkong.wiimoo.common.support;

import java.util.Map;

public enum VipPriceStatusEnum {
    WAITING("WAITING","待审核"),
    ENABLE("ENABLE","启用"),
    DISABLE("DISABLE","停用");

    private String code;
    private String desc;

    VipPriceStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, VipPriceStatusEnum> map = new java.util.HashMap<String, VipPriceStatusEnum>();
    static {
        for (VipPriceStatusEnum statusOfDataDef : VipPriceStatusEnum.values()) {
            map.put(statusOfDataDef.getCode(), statusOfDataDef);
        }
    }
    public static VipPriceStatusEnum getStatusOfDataDef(String code) {
        return map.get(code);
    }
}