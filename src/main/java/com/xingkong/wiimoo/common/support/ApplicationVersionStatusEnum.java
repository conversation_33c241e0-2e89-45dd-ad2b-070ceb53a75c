package com.xingkong.wiimoo.common.support;

import java.util.Map;

public enum ApplicationVersionStatusEnum {
    WAITING("WAITING","待审核"),
    ENABLE("ENABLE","启用"),
    DISABLE("DISABLE","停用");

    private String code;
    private String desc;

    ApplicationVersionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, ApplicationVersionStatusEnum> map = new java.util.HashMap<String, ApplicationVersionStatusEnum>();
    static {
        for (ApplicationVersionStatusEnum statusOfDataDef : ApplicationVersionStatusEnum.values()) {
            map.put(statusOfDataDef.getCode(), statusOfDataDef);
        }
    }
    public static ApplicationVersionStatusEnum getStatusOfDataDef(String code) {
        return map.get(code);
    }
}