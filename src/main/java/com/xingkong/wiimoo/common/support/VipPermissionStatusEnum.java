package com.xingkong.wiimoo.common.support;

import java.util.Map;

public enum VipPermissionStatusEnum {
    ACTIVE("ACTIVE","激活"),
    DELETED("DELETED","已删除");

    private String code;
    private String desc;

    VipPermissionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, VipPermissionStatusEnum> map = new java.util.HashMap<String, VipPermissionStatusEnum>();
    static {
        for (VipPermissionStatusEnum statusOfDataDef : VipPermissionStatusEnum.values()) {
            map.put(statusOfDataDef.getCode(), statusOfDataDef);
        }
    }
    public static VipPermissionStatusEnum getStatusOfDataDef(String code) {
        return map.get(code);
    }
}