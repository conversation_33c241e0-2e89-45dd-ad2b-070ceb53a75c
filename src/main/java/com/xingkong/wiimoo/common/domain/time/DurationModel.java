package com.xingkong.wiimoo.common.domain.time;

import com.xingkong.base.infrastructure.ability.exception.ResultException;
import com.xingkong.base.infrastructure.ability.support.Result;

public class DurationModel {
    private Integer duration;
    private DurationUnit unit;

    private  DurationModel(){}

    public static DurationModel create(Integer duration, String unit) {
        if (duration == null || duration < 0) {
            throw new ResultException(Result.paramError("duration cannot be null or negative"));
        }

        DurationUnit durationUnit = DurationUnit.getDurationUnit(unit);
        if (durationUnit == null) {
            throw new ResultException(Result.paramError("durationUnit error"));
        }

        DurationModel durationModel = new DurationModel();
        durationModel.duration = duration;
        durationModel.unit = durationUnit;
        return durationModel;
    }

    public Integer getSeconds() {
        switch (unit) {
            case YEAR:
                return duration * 365 * 24 * 60 * 60;
            case MONTH:
                return duration * 30 * 24 * 60 * 60;
            case DAY:
                return duration * 24 * 60 * 60;
            case HOUR:
                return duration * 60 * 60;
            case MINUTE:
                return duration * 60;
            case SECOND:
                return duration;
            default:
                return 0;
        }
    }
}