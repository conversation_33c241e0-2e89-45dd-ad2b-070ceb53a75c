package com.xingkong.wiimoo.common.domain.time;

import java.util.HashMap;
import java.util.Map;

public enum DurationUnit {
    YEAR("year", "年"),
    MONTH("month", "月"),
    DAY("day", "日"),
    HOUR("hour", "时"),
    MINUTE("minute", "分"),
    SECOND("second", "秒");

    private String code;
    private String desc;

    DurationUnit(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static Map<String, DurationUnit> map = new HashMap<String, DurationUnit>();
    static {
        for (DurationUnit durationUnit : DurationUnit.values()) {
            map.put(durationUnit.getCode(), durationUnit);
        }
    }

    public static DurationUnit getDurationUnit(String code) {
        return map.get(code);
    }
}