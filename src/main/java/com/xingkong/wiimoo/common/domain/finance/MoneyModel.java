package com.xingkong.wiimoo.common.domain.finance;

import com.xingkong.base.infrastructure.ability.exception.ResultException;
import com.xingkong.base.infrastructure.ability.support.Result;

import java.math.BigDecimal;

public class MoneyModel {

    private BigDecimal amount;
    private Currency currency;

    private MoneyModel() {}

    public static MoneyModel create(BigDecimal amount) {
        return create(amount, Currency.CNY);
    }

    public static MoneyModel create(BigDecimal amount, Currency currency) {

        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0 || amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ResultException(Result.paramError("货币金额不能为空"));
        }

        if (currency == null) {
            throw new ResultException(Result.paramError("货币种类不能为空"));
        }
        MoneyModel moneyModel = new MoneyModel();
        moneyModel.amount = amount;
        moneyModel.currency = currency;
        return moneyModel;
    }

    public MoneyModel add(MoneyModel moneyModel) {
        if (!this.currency.equals(moneyModel.getCurrency())) {
            throw new ResultException(Result.businessError("货币种类不一致"));
        }
        return  MoneyModel.create(this.amount.add(moneyModel.getAmount()), this.currency);
    }

    public MoneyModel subtract(MoneyModel moneyModel) {
        if (!this.currency.equals(moneyModel.getCurrency())) {
            throw new ResultException(Result.businessError("货币种类不一致"));
        }
        return MoneyModel.create(this.amount.subtract(moneyModel.getAmount()), this.currency);
    }

    public MoneyModel multiply(BigDecimal factor) {
        if (factor == null || factor.compareTo(BigDecimal.ZERO) == 0 || factor.compareTo(BigDecimal.ZERO) < 0) {
            throw new ResultException(Result.paramError("factor cannot be null"));
        }
        return MoneyModel.create(this.amount.multiply(factor), this.currency);
    }

    public MoneyModel divide(BigDecimal factor) {
        if (factor == null || factor.compareTo(BigDecimal.ZERO) == 0 || factor.compareTo(BigDecimal.ZERO) < 0) {
            throw new ResultException(Result.paramError("factor cannot be null"));
        }
        return MoneyModel.create(this.amount.divide(factor), this.currency);
    }


    public BigDecimal getAmount() {
        return amount;
    }

    public Currency getCurrency() {
        return currency;
    }
}