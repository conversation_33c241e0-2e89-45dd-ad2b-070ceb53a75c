package com.xingkong.wiimoo.common.domain;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;

import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 手机号值对象
 * 封装手机号的核心业务逻辑和验证规则
 */
public class PhoneNumber {
    private static final Pattern DIGITS_ONLY = Pattern.compile("^\\d+$");
    // 原始手机号字符串
    private final String rawPhoneNumber;

    // 国家码
    private final int countryCode;

    // 国内号码部分
    private final String nationalNumber;

    // 是否有效
    private final boolean valid;

    // 所属地区代码（ISO 3166-1 alpha-2）
    private final String regionCode;

    /**
     * 私有构造方法，强制使用工厂方法创建
     */
    private PhoneNumber(String rawPhoneNumber, int countryCode,
                        String nationalNumber, boolean valid, String regionCode) {
        this.rawPhoneNumber = Objects.requireNonNull(rawPhoneNumber);
        this.countryCode = countryCode;
        this.nationalNumber = nationalNumber;
        this.valid = valid;
        this.regionCode = regionCode;
    }

    /**
     * 增强的工厂方法，支持多种格式
     */
    public static PhoneNumber fromString(String phoneNumber) {
        PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

        try {
            // 尝试直接解析
            return parsePhoneNumber(phoneUtil, phoneNumber);
        } catch (IllegalArgumentException e) {
            // 如果是纯数字且以国家码开头，尝试添加+号解析
            if (DIGITS_ONLY.matcher(phoneNumber).matches() && phoneNumber.length() > 3) {
                try {
                    return parsePhoneNumber(phoneUtil, "+" + phoneNumber);
                } catch (IllegalArgumentException ignored) {
                    // 继续尝试其他方式
                }
            }

            // 尝试作为国内号码解析（默认中国区）
            try {
                Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(phoneNumber, "CN");
                return new PhoneNumber(
                        phoneNumber,
                        parsedNumber.getCountryCode(),
                        String.valueOf(parsedNumber.getNationalNumber()),
                        phoneUtil.isValidNumberForRegion(parsedNumber, "CN"),
                        phoneUtil.getRegionCodeForNumber(parsedNumber)
                );
            } catch (NumberParseException ex) {
                throw new IllegalArgumentException("无效的手机号格式: " + phoneNumber, ex);
            }
        }
    }

    private static PhoneNumber parsePhoneNumber(PhoneNumberUtil phoneUtil, String phoneNumber) {
        try {
            Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(phoneNumber, null);
            return new PhoneNumber(
                    phoneNumber,
                    parsedNumber.getCountryCode(),
                    String.valueOf(parsedNumber.getNationalNumber()),
                    phoneUtil.isValidNumber(parsedNumber),
                    phoneUtil.getRegionCodeForNumber(parsedNumber)
            );
        } catch (NumberParseException e) {
            throw new IllegalArgumentException("无效的手机号格式: " + phoneNumber, e);
        }
    }


    /**
     * 工厂方法 - 从国家码和国内号码创建
     *
     * @param countryCode 国家码
     * @param nationalNumber 国内号码
     * @return PhoneNumber 领域对象
     */
    public static PhoneNumber fromParts(int countryCode, String nationalNumber) {
        String formatted = "+" + countryCode + nationalNumber;
        return fromString(formatted);
    }

    /**
     * 是否包含国家码
     */
    public boolean hasCountryCode() {
        return countryCode != 0;
    }

    /**
     * 是否属于指定地区
     *
     * @param regionCode 地区代码 (如 "CN", "US")
     */
    public boolean isFromRegion(String regionCode) {
        return regionCode != null && regionCode.equalsIgnoreCase(this.regionCode);
    }

    /**
     * 验证手机号是否有效
     */
    public boolean isValid() {
        return valid;
    }

    @Override
    public String toString() {
        return rawPhoneNumber;
    }

    public String getRawPhoneNumber() {
        return rawPhoneNumber;
    }

    public int getCountryCode() {
        return countryCode;
    }

    public String getNationalNumber() {
        return nationalNumber;
    }

    public String getRegionCode() {
        return regionCode;
    }
}