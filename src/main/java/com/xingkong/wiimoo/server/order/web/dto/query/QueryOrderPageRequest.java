package com.xingkong.wiimoo.server.order.web.dto.query;

import com.xingkong.base.infrastructure.ability.validation.Condition;
import com.xingkong.base.infrastructure.ability.validation.ConditionalValidation;
import com.xingkong.wiimoo.common.support.MyPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "查询订单请求对象")
@ConditionalValidation
public class QueryOrderPageRequest {
    @ApiModelProperty(value = "分页信息", required = true)
    @NotNull(message = "分页信息不能为空")
    @Valid
    private MyPage page;
    @ApiModelProperty(value = "订单号", required = false)
    @Condition(when = "consumerId==null&&orderStatus==null", then = "orderNo!=null", message = "订单号不能为空")
    private String orderNo;
    @ApiModelProperty(value = "用户id", required = false)
    @Condition( when = "orderNo==null&&orderStatus==null", then = "consumerId!=null", message = "用户id不能为空")
    private String consumerId;
    @ApiModelProperty(value = "订单状态，为空则查询所有状态", required = false)
    private List<String> orderStatus;

    public MyPage getPage() {
        return page;
    }

    public void setPage(MyPage page) {
        this.page = page;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(String consumerId) {
        this.consumerId = consumerId;
    }

    public List<String> getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(List<String> orderStatus) {
        this.orderStatus = orderStatus;
    }
}