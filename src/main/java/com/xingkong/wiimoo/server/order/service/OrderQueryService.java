package com.xingkong.wiimoo.server.order.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.order.db.entity.OrderEntity;
import com.xingkong.wiimoo.server.order.db.entity.OrderItemEntity;
import com.xingkong.wiimoo.server.order.support.OrderStatus;
import com.xingkong.wiimoo.server.order.web.dto.query.QueryOrderPageRequest;
import com.xingkong.wiimoo.server.order.web.dto.query.QueryOrderResponseData;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

import static com.xingkong.wiimoo.server.order.db.entity.table.OrderItemTableDef.OrderItem;
import static com.xingkong.wiimoo.server.order.db.entity.table.OrderTableDef.Order;

@Service
@Validated
public class OrderQueryService {


    public Result<Page<QueryOrderResponseData>> queryOrder(@Valid QueryOrderPageRequest queryOrderPageRequest) {
        if (queryOrderPageRequest.getOrderStatus() != null) {
            for (String orderStatus : queryOrderPageRequest.getOrderStatus()) {
                if (OrderStatus.getOrderStatus(orderStatus) == null) {
                    return Result.businessError("订单状态不合法");
                }
            }
        }

        Page<QueryOrderResponseData> queryOrderResponseDataList = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(queryOrderPageRequest.getOrderNo()).when(!StringUtils.isEmpty(queryOrderPageRequest.getOrderNo())))
                .and(Order.ConsumerId.eq(queryOrderPageRequest.getConsumerId()).when(!StringUtils.isEmpty(queryOrderPageRequest.getConsumerId())))
                .and(Order.OrderStatus.in(queryOrderPageRequest.getOrderStatus()).when(!CollectionUtils.isEmpty(queryOrderPageRequest.getOrderStatus())))
                .orderBy(Order.UpdatedTime, false)
                .pageAs(new Page<>(queryOrderPageRequest.getPage().getPageNumber(), queryOrderPageRequest.getPage().getPageSize()), QueryOrderResponseData.class);

        queryOrderResponseDataList.getRecords().forEach(queryOrderResponseData -> {
            queryOrderResponseData.setOrderItemInfos(QueryChain.of(OrderItemEntity.class).select()
                    .from(OrderItemEntity.class)
                    .where(OrderItem.OrderNo.eq(queryOrderResponseData.getOrderNo()))
                    .listAs(QueryOrderResponseData.OrderItemInfo.class)
            );
        });

        return Result.success(queryOrderResponseDataList);
    }

    public Result<QueryOrderResponseData> queryOrder(String orderNo) {
        QueryOrderResponseData queryOrderResponseData = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(orderNo))
                .oneAs(QueryOrderResponseData.class);
        if (queryOrderResponseData == null) {
            return Result.businessError("订单不存在");
        }
        queryOrderResponseData.setOrderItemInfos(QueryChain.of(OrderItemEntity.class).select()
                .from(OrderItemEntity.class)
                .where(OrderItem.OrderNo.eq(orderNo))
                .listAs(QueryOrderResponseData.OrderItemInfo.class)
        );
        return Result.success(queryOrderResponseData);
    }

    public List<OrderEntity> queryWaitClosedOrder() {

        return QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderStatus.in(OrderStatus.CREATED.getCode(), OrderStatus.WAIT_PAY.getCode()))
                .and(Order.ExpireTime.lt(LocalDateTime.now()))
                .orderBy(Order.UpdatedTime, true)
                .list();
    }

    public List<OrderEntity> queryPayingOrder() {
        return QueryChain.of(OrderEntity.class).select().from(OrderEntity.class)
                .where(Order.OrderStatus.eq(OrderStatus.WAIT_PAY.getCode()))
                .and(Order.ExpireTime.lt(LocalDateTime.now()))
                .orderBy(Order.UpdatedTime, true)
                .list();
    }
}