package com.xingkong.wiimoo.server.order.service;

import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.order.db.entity.OrderEntity;
import com.xingkong.wiimoo.server.order.support.OrderStatus;
import com.xingkong.wiimoo.server.pay.db.PayItemEntity;
import com.xingkong.wiimoo.server.pay.support.PayStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.xingkong.wiimoo.server.order.db.entity.table.OrderTableDef.Order;
import static com.xingkong.wiimoo.server.pay.db.table.PayItemTableDef.PayItem;

@Service
public class OrderValidateService {

    public Result canPay(OrderEntity orderEntity) {
        if (orderEntity == null) {
            return Result.businessError("订单不存在");
        }
        if (OrderStatus.PAID.getCode().equals(orderEntity.getOrderStatus())) {
            return Result.businessError("已支付");
        }
        if (OrderStatus.CLOSED.getCode().equals(orderEntity.getOrderStatus())) {
            return Result.businessError("订单已关闭");
        }
        if (orderEntity.getExpireTime().isBefore(LocalDateTime.now())) {
            return Result.businessError("订单已过期");
        }
        return Result.success();
    }

    public Result canClose(String orderNo) {
        OrderEntity orderEntity = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(orderNo))
                .one();
        if (orderEntity == null) {
            return Result.businessError("订单不存在");
        }
        if (OrderStatus.CREATED.getCode().equals(orderEntity.getOrderStatus())) {
            return Result.success();
        }
        if (OrderStatus.WAIT_PAY.getCode().equals(orderEntity.getOrderStatus())) {
            List<PayItemEntity> payItemEntityList = QueryChain.of(PayItemEntity.class).select()
                    .from(PayItemEntity.class)
                    .where(PayItem.OrderNo.eq(orderNo))
                    .list();
            boolean canClose = true;
            for (PayItemEntity payItemEntity : payItemEntityList) {
                if (!PayStatus.CLOSE.getCode().equals(payItemEntity.getStatus())) {
                    canClose = false;
                    break;
                }
            }
            if (canClose) {
                return Result.success();
            }
            return Result.businessError("还有支付项未关闭，不可关闭订单");
        }
        return Result.businessError("不可关闭");
    }
}