package com.xingkong.wiimoo.server.order.support;

import java.util.Map;

public enum SkuType {
    VIP("vip", "会员"),
    SCORE("score", "积分");

    private String code;
    private String desc;

    SkuType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, SkuType> map = new java.util.HashMap<String, SkuType>();

    static {
        for (SkuType skuType : SkuType.values()) {
            map.put(skuType.getCode(), skuType);
        }
    }

    public static SkuType getSkuType(String code) {
        return map.get(code);
    }

}