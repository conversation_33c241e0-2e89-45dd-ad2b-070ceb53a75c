package com.xingkong.wiimoo.server.order.support;

import java.util.HashMap;
import java.util.Map;

public enum OrderStatus {
    CREATED("CREATED","未支付"),
    WAIT_PAY("WAIT_PAY","等待支付"),
    PAID("PAID","已支付"),
    CLOSED("CLOSED","已关闭");
    private String code;
    private String desc;
    OrderStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static Map<String, OrderStatus> map = new HashMap<String, OrderStatus>();

    static {
        for (OrderStatus orderStatus : OrderStatus.values()) {
            map.put(orderStatus.getCode(), orderStatus);
        }
    }
    public static OrderStatus getOrderStatus(String code) {
        return map.get(code);
    }

}