package com.xingkong.wiimoo.server.order.web.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "获取支付地址响应对象")
public class GetPayUrlResponseData {
    @ApiModelProperty(value = "是否可以支付", required = true)
    private Boolean canPay;
    @ApiModelProperty(value = "订单状态", required = true)
    private String orderStatus;
    @ApiModelProperty(value = "支付地址", required = true)
    private String payUrl;
    @ApiModelProperty(value = "交易流水号", required = true)
    private String payNo;

    public Boolean getCanPay() {
        return canPay;
    }

    public void setCanPay(Boolean canPay) {
        this.canPay = canPay;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

}