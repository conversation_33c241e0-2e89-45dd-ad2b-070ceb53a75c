package com.xingkong.wiimoo.server.order.service;

import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.application.service.VipValidateService;
import com.xingkong.wiimoo.server.order.support.SkuType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class SkuValidateService {
    @Autowired
    private VipValidateService vipValidateService;

    public Result isValid(String skuId, String skuType, BigDecimal skuPrice) {
        SkuType skuTypeEnum = SkuType.getSkuType(skuType);
        switch (skuTypeEnum) {
            case VIP:
                return vipValidateService.isValid(skuId, skuPrice);
            case SCORE:
                break;
            default:
                break;
        }

        return Result.success();
    }
}