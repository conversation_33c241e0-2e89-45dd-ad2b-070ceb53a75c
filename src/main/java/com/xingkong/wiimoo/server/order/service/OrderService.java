package com.xingkong.wiimoo.server.order.service;

import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.base.infrastructure.ability.lock.distributed.DistributedLock;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.base.infrastructure.ability.util.DLockUtil;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.application.db.entity.VipPriceEntity;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerVipEntity;
import com.xingkong.wiimoo.server.consumer.service.ConsumerValidateService;
import com.xingkong.wiimoo.server.order.db.entity.OrderEntity;
import com.xingkong.wiimoo.server.order.db.entity.OrderItemEntity;
import com.xingkong.wiimoo.server.order.dto.PayInfo;
import com.xingkong.wiimoo.server.order.dto.PayResultInfo;
import com.xingkong.wiimoo.server.order.support.OrderStatus;
import com.xingkong.wiimoo.server.order.support.PayMode;
import com.xingkong.wiimoo.server.order.support.SkuType;
import com.xingkong.wiimoo.server.order.web.dto.create.CreateOrderRequest;
import com.xingkong.wiimoo.server.order.web.dto.create.CreateOrderResponseData;
import com.xingkong.wiimoo.server.order.web.dto.pay.GetOrderPayUrlRequest;
import com.xingkong.wiimoo.server.order.web.dto.pay.GetPayUrlResponseData;
import com.xingkong.wiimoo.server.pay.service.PayService;
import com.xingkong.wiimoo.server.pay.support.PayStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xingkong.wiimoo.server.application.db.entity.table.VipPriceTableDef.VipPrice;
import static com.xingkong.wiimoo.server.consumer.db.entity.table.ConsumerVipTableDef.ConsumerVip;
import static com.xingkong.wiimoo.server.order.db.entity.table.OrderItemTableDef.OrderItem;
import static com.xingkong.wiimoo.server.order.db.entity.table.OrderTableDef.Order;

@Service
@Validated
public class OrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderService.class);
    @Autowired
    private SkuValidateService skuValidateService;
    @Autowired
    private ConsumerValidateService consumerValidateService;
    @Autowired
    private OrderValidateService orderValidateService;
    @Autowired
    private PayService payService;

    @Transactional
    public Result<CreateOrderResponseData> createOrder(@Valid CreateOrderRequest createOrderRequest) {
        Result result = checkCreateOrderRequest(createOrderRequest);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }

        for (CreateOrderRequest.OrderItemInfo orderItemInfo : createOrderRequest.getOrderItemInfos()) {
            if (orderItemInfo.getSkuType().equals(SkuType.VIP.getCode())) {
                VipPriceEntity vipPriceEntity = QueryChain.of(VipPriceEntity.class).select()
                        .from(VipPriceEntity.class)
                        .where(VipPrice.Id.eq(orderItemInfo.getSkuId()))
                        .one();
                if (vipPriceEntity.getBuyTimes() == 0) {
                    continue;
                }
                long count = QueryChain.of(ConsumerVipEntity.class).select()
                        .from(ConsumerVipEntity.class)
                        .where(ConsumerVip.ConsumerId.eq(StpKit.STORE.getLoginId().toString()))
                        .and(ConsumerVip.VipId.eq(vipPriceEntity.getVipId()))
                        .count();
                if (count > vipPriceEntity.getBuyTimes()) {
                    return Result.businessError(orderItemInfo.getSkuName() + "已达到购买次数上限");
                }
            }
        }

        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setOrderNo(UlidCreator.getUlid().toString());
        orderEntity.setConsumerId(StpKit.STORE.getLoginId().toString());
        orderEntity.setTotalAmount(createOrderRequest.getTotalAmount());
        orderEntity.setPayAmount(createOrderRequest.getTotalAmount());
        orderEntity.setManualAmount(BigDecimal.ZERO);
        orderEntity.setOrderStatus(OrderStatus.CREATED.getCode());
        LocalDateTime now = LocalDateTime.now();
        orderEntity.setExpireTime(now.plusMinutes(30));
        orderEntity.setCreatedTime(now);
        orderEntity.setUpdatedTime(now);
        DbChain.table(OrderEntity.class).save(orderEntity);

        for (CreateOrderRequest.OrderItemInfo orderItemInfo : createOrderRequest.getOrderItemInfos()) {
            OrderItemEntity orderItemEntity = new OrderItemEntity();
            orderItemEntity.setId(UlidCreator.getUlid().toString());
            orderItemEntity.setOrderNo(orderEntity.getOrderNo());
            orderItemEntity.setSkuType(orderItemInfo.getSkuType());
            orderItemEntity.setSkuId(orderItemInfo.getSkuId());
            orderItemEntity.setSkuName(orderItemInfo.getSkuName());
            orderItemEntity.setSkuPrice(orderItemInfo.getSkuPrice());
            orderItemEntity.setSkuQuantity(orderItemInfo.getSkuQuantity());
            orderItemEntity.setTotalAmount(orderItemInfo.getTotalAmount());
            orderItemEntity.setPayAmount(orderItemInfo.getTotalAmount());
            orderItemEntity.setManualAmount(BigDecimal.ZERO);
            orderItemEntity.setCreatedTime(now);
            orderItemEntity.setUpdateTime(now);
            DbChain.table(OrderItemEntity.class).save(orderItemEntity);
        }

        CreateOrderResponseData createOrderResponseData = new CreateOrderResponseData();
        createOrderResponseData.setOrderNo(orderEntity.getOrderNo());
        createOrderResponseData.setExpireTime(orderEntity.getExpireTime());

        if (createOrderRequest.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
            PayResultInfo payResultInfo = new PayResultInfo();
            payResultInfo.setOrderNo(orderEntity.getOrderNo());
            payResultInfo.setPayStatus(PayStatus.SUCCESS.getCode());
            payResultInfo.setPayTime(LocalDateTime.now());
            payResultInfo.setAmount(createOrderRequest.getTotalAmount());
            payResultInfo.setPayMode(PayMode.NONE.getCode());

            payService.dealPayResult(payResultInfo);
        }

        return Result.success(createOrderResponseData);
    }

    private Result checkCreateOrderRequest(CreateOrderRequest createOrderRequest) {
        //校验订单层各种金额是否自洽
        //校验订单项各种金额是否自洽
        BigDecimal orderItemTotalAmount = BigDecimal.ZERO;
        for (CreateOrderRequest.OrderItemInfo orderItemInfo : createOrderRequest.getOrderItemInfos()) {
            if (orderItemInfo.getTotalAmount().compareTo(orderItemInfo.getSkuPrice().multiply(BigDecimal.valueOf(orderItemInfo.getSkuQuantity()))) < 0) {
                return Result.businessError("订单商品" + orderItemInfo.getSkuId() + "金额错误");
            }
            orderItemTotalAmount = orderItemTotalAmount.add(orderItemInfo.getTotalAmount());
        }
        if (createOrderRequest.getTotalAmount().compareTo(orderItemTotalAmount) < 0) {
            return Result.businessError("订单总金额与订单所有商品总金额不一致");
        }
        //校验用户是否可用
        Result result = consumerValidateService.isValid(StpKit.STORE.getLoginId().toString());
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }
        //校验商品是否可用
        for (CreateOrderRequest.OrderItemInfo orderItemInfo : createOrderRequest.getOrderItemInfos()) {
            result = skuValidateService.isValid(orderItemInfo.getSkuId(), orderItemInfo.getSkuType(), orderItemInfo.getSkuPrice());
            if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
                return result;
            }
        }
        return Result.success(null);
    }

    @Transactional
    public Result<GetPayUrlResponseData> getPayUrl(@Valid GetOrderPayUrlRequest getOrderPayUrlRequest) {
        OrderEntity orderEntity = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(getOrderPayUrlRequest.getOrderNo()))
                .one();
        Result result = orderValidateService.canPay(orderEntity);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }

        List<OrderItemEntity> orderItemEntityList = QueryChain.of(OrderItemEntity.class).select()
                .from(OrderItemEntity.class)
                .where(OrderItem.OrderNo.eq(getOrderPayUrlRequest.getOrderNo()))
                .list();

        PayInfo payInfo = new PayInfo();
        payInfo.setPayNo(UlidCreator.getUlid().toString());
        payInfo.setOrderNo(getOrderPayUrlRequest.getOrderNo());
        payInfo.setTotalAmount(orderEntity.getPayAmount());
        payInfo.setPayMode(getOrderPayUrlRequest.getPayMode());
        payInfo.setSkuName(orderItemEntityList.stream().map(OrderItemEntity::getSkuName).collect(Collectors.joining(" ")));
        payInfo.setReturnUrl(getOrderPayUrlRequest.getReturnUrl());
        payInfo.setExpireTime(orderEntity.getExpireTime());

        Result<GetPayUrlResponseData> getPayUrlResponseDataResult = payService.getPayUrl(payInfo);
        if (!ResultCode.SUCCESS.getCode().equals(getPayUrlResponseDataResult.getResultCode())) {
            return getPayUrlResponseDataResult;
        }
        GetPayUrlResponseData getPayUrlResponseData = getPayUrlResponseDataResult.getData();
        getPayUrlResponseData.setCanPay(true);
        getPayUrlResponseData.setOrderStatus(OrderStatus.WAIT_PAY.getCode());
        UpdateChain.of(OrderEntity.class)
                .set(Order.PayNo, getPayUrlResponseData.getPayNo())
                .set(Order.OrderStatus, OrderStatus.WAIT_PAY.getCode())
                .set(Order.UpdatedTime, LocalDateTime.now())
                .where(Order.OrderNo.eq(getOrderPayUrlRequest.getOrderNo()))
                .update();

        return Result.success(getPayUrlResponseData);
    }

    @Transactional
    public void closeOrder(String orderNo) {
        DistributedLock distributedLock = DLockUtil.getLock();
        boolean isLock = distributedLock.tryLock(orderNo, 5, 1000 * 60, TimeUnit.MICROSECONDS);
        if (!isLock) {
            LOGGER.info("获取锁失败:{}", orderNo);
            return;
        }
        Result result = orderValidateService.canClose(orderNo);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            distributedLock.unlock();
            LOGGER.info("关闭订单:{}，失败，原因为：{}", orderNo, result.getResultMsg());
            return;
        }

        UpdateChain.of(OrderEntity.class)
                .set(Order.OrderStatus, OrderStatus.CLOSED.getCode())
                .set(Order.UpdatedTime, LocalDateTime.now())
                .where(Order.OrderNo.eq(orderNo))
                .update();
        LOGGER.info("关闭订单成功:{}", orderNo);
        distributedLock.unlock();
    }
}