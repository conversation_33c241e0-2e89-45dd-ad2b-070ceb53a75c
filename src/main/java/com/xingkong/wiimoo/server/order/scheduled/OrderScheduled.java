package com.xingkong.wiimoo.server.order.scheduled;

import com.xingkong.wiimoo.server.order.db.entity.OrderEntity;
import com.xingkong.wiimoo.server.order.service.OrderQueryService;
import com.xingkong.wiimoo.server.order.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@EnableScheduling
@ConditionalOnProperty(name = "task.order.enabled")
public class OrderScheduled {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderScheduled.class);

    @Autowired
    private OrderQueryService orderQueryService;
    @Autowired
    private OrderService orderService;

    @Scheduled(cron = "0 0/1 * * * ?")
    @Async("closeOrderScheduler")
    public void closeOrder() {
        LOGGER.info("========关闭订单任务开始执行=========");
        List<OrderEntity> orderEntityList = orderQueryService.queryWaitClosedOrder();
        LOGGER.info("待关闭订单数:{}", orderEntityList.size());
        orderEntityList.forEach(orderEntity -> {
            LOGGER.info("开始关闭订单任务:{}", orderEntity.getOrderNo());
            orderService.closeOrder(orderEntity.getOrderNo());
            LOGGER.info("完成关闭订单任务:{}", orderEntity.getOrderNo());
        });
        LOGGER.info("========关闭订单任务结束执行=========");
    }
}