package com.xingkong.wiimoo.server.order.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckOr;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.order.service.OrderService;
import com.xingkong.wiimoo.server.order.web.dto.create.CreateOrderRequest;
import com.xingkong.wiimoo.server.order.web.dto.create.CreateOrderResponseData;
import com.xingkong.wiimoo.server.order.web.dto.pay.GetOrderPayUrlRequest;
import com.xingkong.wiimoo.server.order.web.dto.pay.GetPayUrlResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "订单服务接口")
@RestController
@RequestMapping("/order")
@SaCheckOr(login = {@SaCheckLogin(type = StpKit.ADMIN_TYPE), @SaCheckLogin(type = StpKit.STORE_TYPE)})
public class OrderController {

    @Autowired
    private OrderService orderService;


    @ApiOperation(value = "创建订单接口", notes = "创建订单")
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    @PostMapping("/create")
    @ResponseBody
    public Result<CreateOrderResponseData> createOrder(@RequestBody CreateOrderRequest createOrderRequest) {
        return orderService.createOrder(createOrderRequest);
    }

    @ApiOperation(value = "支付订单接口", notes = "支付订单")
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    @PostMapping("/get/payUrl")
    @ResponseBody
    public Result<GetPayUrlResponseData> getPayUrl(@RequestBody GetOrderPayUrlRequest getOrderPayUrlRequest) {
        return orderService.getPayUrl(getOrderPayUrlRequest);
    }
}