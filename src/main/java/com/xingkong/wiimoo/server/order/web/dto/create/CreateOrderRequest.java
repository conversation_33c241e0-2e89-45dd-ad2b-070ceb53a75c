package com.xingkong.wiimoo.server.order.web.dto.create;

import com.xingkong.base.infrastructure.ability.validation.ConditionalValidation;
import com.xingkong.base.infrastructure.ability.validation.EnumValidation;
import com.xingkong.wiimoo.server.order.support.SkuType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "创建订单请求对象")
@ConditionalValidation
public class CreateOrderRequest {
    @ApiModelProperty(value = "订单总金额",required = true,example = "100.86")
    @NotNull(message = "订单总金额不能为空")
    @Min(value = 0, message = "订单总金额不能小于0")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "订单明细信息",required = true)
    @NotNull(message = "订单明细信息不能为空")
    @NotEmpty(message = "订单明细信息不能为空")
    @Valid
    private List<OrderItemInfo> orderItemInfos;

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<OrderItemInfo> getOrderItemInfos() {
        return orderItemInfos;
    }

    public void setOrderItemInfos(List<OrderItemInfo> orderItemInfos) {
        this.orderItemInfos = orderItemInfos;
    }

    @ApiModel(description = "订单明细信息")
    @ConditionalValidation
    public static class OrderItemInfo {
        @ApiModelProperty(value = "应用编码", required = true)
        @NotBlank(message = "应用编码不能为空")
        private String applicationCode;
        @ApiModelProperty(value = "商品id", required = true)
        @NotBlank(message = "商品id不能为空")
        private String skuId;
        @ApiModelProperty(value = "商品名称", required = true)
        @NotBlank(message = "商品名称不能为空")
        private String skuName;
        @ApiModelProperty(value = "商品类型", required = true)
        @NotNull(message = "商品类型不能为空")
        @EnumValidation(enumClass = SkuType.class, message = "不合法的商品类型")
        private String skuType;
        @ApiModelProperty(value = "商品单价", required = true)
        @NotNull(message = "商品单价不能为空")
        @Min(value = 0, message = "商品单价不能小于0")
        private BigDecimal skuPrice;
        @ApiModelProperty(value = "商品数量", required = true)
        @NotNull(message = "商品数量不能为空")
        @Min(value = 0, message = "商品数量不能小于0")
        private Integer skuQuantity;
        @ApiModelProperty(value = "总金额", required = true)
        @NotNull(message = "总金额不能为空")
        @Min(value = 0, message = "总金额不能小于0")
        private BigDecimal totalAmount;

        public String getSkuName() {
            return skuName;
        }

        public void setSkuName(String skuName) {
            this.skuName = skuName;
        }

        public String getApplicationCode() {
            return applicationCode;
        }

        public void setApplicationCode(String applicationCode) {
            this.applicationCode = applicationCode;
        }

        public String getSkuId() {
            return skuId;
        }

        public void setSkuId(String skuId) {
            this.skuId = skuId;
        }

        public String getSkuType() {
            return skuType;
        }

        public void setSkuType(String skuType) {
            this.skuType = skuType;
        }

        public BigDecimal getSkuPrice() {
            return skuPrice;
        }

        public void setSkuPrice(BigDecimal skuPrice) {
            this.skuPrice = skuPrice;
        }

        public Integer getSkuQuantity() {
            return skuQuantity;
        }

        public void setSkuQuantity(Integer skuQuantity) {
            this.skuQuantity = skuQuantity;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public void setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
        }

    }
}