package com.xingkong.wiimoo.server.order.web.dto.pay;

import com.xingkong.base.infrastructure.ability.validation.EnumValidation;
import com.xingkong.wiimoo.server.order.support.PayMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "支付订单请求对象")
public class GetOrderPayUrlRequest {
    @ApiModelProperty(value = "订单号", required = true)
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    @ApiModelProperty(value = "支付方式", required = true)
    @NotBlank(message = "支付方式不能为空")
    @EnumValidation(enumClass = PayMode.class, message = "不合法的支付方式")
    private String payMode;
    @ApiModelProperty(value = "支付成功后跳转的页面", required = false)
    private String returnUrl;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }
}