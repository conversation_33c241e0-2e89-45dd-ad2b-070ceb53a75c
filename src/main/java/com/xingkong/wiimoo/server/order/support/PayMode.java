package com.xingkong.wiimoo.server.order.support;

import java.util.Map;

public enum PayMode {
    ALIPAY("ALIPAY", "支付宝"),
    WECHAT("WECHAT", "微信"),
    NONE("NONE", "无");

    private String code;
    private String desc;

    PayMode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, PayMode> map = new java.util.HashMap<String, PayMode>();

    static {
        for (PayMode payMode : PayMode.values()) {
            map.put(payMode.getCode(), payMode);
        }
    }

    public static PayMode getPayMode(String code) {
        return map.get(code);
    }
}