package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "更新预设请求对象")
public class UpdatePresetRequest {
    @ApiModelProperty(value = "预设id", required = true)
    @NotBlank(message = "预设id不能为空")
    private String id;
    @ApiModelProperty(value = "预设名称", required = true)
    @NotBlank(message = "预设名称不能为空")
    private String name;
    @ApiModelProperty(value = "预设内容", required = true)
    @NotBlank(message = "预设内容不能为空")
    private String content;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}