package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel(description = "用户VIP信息")
public class QueryMyVipResponseData {
    @ApiModelProperty(value = "应用编码", required = true)
    private String applicationCode;
    @ApiModelProperty(value = "应用名称", required = true)
    private String applicationName;
    @ApiModelProperty(value = "VIP编码", required = true)
    private String vipCode;
    @ApiModelProperty(value = "VIP名称", required = true)
    private String vipName;
    @ApiModelProperty(value = "VIP开始时间", required = true)
    private LocalDateTime vipStartTime;
    @ApiModelProperty(value = "VIP结束时间", required = true)
    private LocalDateTime vipEndTime;

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getVipCode() {
        return vipCode;
    }

    public void setVipCode(String vipCode) {
        this.vipCode = vipCode;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public LocalDateTime getVipStartTime() {
        return vipStartTime;
    }

    public void setVipStartTime(LocalDateTime vipStartTime) {
        this.vipStartTime = vipStartTime;
    }

    public LocalDateTime getVipEndTime() {
        return vipEndTime;
    }

    public void setVipEndTime(LocalDateTime vipEndTime) {
        this.vipEndTime = vipEndTime;
    }
}