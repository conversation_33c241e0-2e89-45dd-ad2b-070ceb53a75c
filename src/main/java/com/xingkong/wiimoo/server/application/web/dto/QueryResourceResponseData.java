package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel(description = "查询资源信息响应对象")
public class QueryResourceResponseData {
    @ApiModelProperty(value = "资源id", required = true)
    private String id;
    @ApiModelProperty(value = "应用编码", required = true)
    private String applicationCode;
    @ApiModelProperty(value = "资源编码", required = true)
    private String code;
    @ApiModelProperty(value = "资源名称", required = true)
    private String name;
    @ApiModelProperty(value = "资源说明", required = true)
    private String description;
    @ApiModelProperty(value = "资源类型", required = true)
    private String type;
    @ApiModelProperty(value = "每次使用的积分", required = true)
    private Integer eachUseScore;
    @ApiModelProperty(value = "资源状态", required = true)
    private String status;
    @ApiModelProperty(value = "资源生效时间", required = true)
    private LocalDateTime startTime;
    @ApiModelProperty(value = "资源失效时间", required = true)
    private LocalDateTime endTime;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;
    @ApiModelProperty(value = "更新时间", required = true)
    private LocalDateTime updatedTime;

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getEachUseScore() {
        return eachUseScore;
    }

    public void setEachUseScore(Integer eachUseScore) {
        this.eachUseScore = eachUseScore;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
}