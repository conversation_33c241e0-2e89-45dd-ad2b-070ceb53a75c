package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel(description = "查询应用定义信息响应对象")
public class QueryApplicationResponseData {
    @ApiModelProperty(value = "应用id", required = true)
    private String id;
    @ApiModelProperty(value = "应用编码", required = true)
    private String code;
    @ApiModelProperty(value = "应用名称", required = true)
    private String name;
    @ApiModelProperty(value = "应用描述", required = true)
    private String description;
    @ApiModelProperty(value = "应用主图地址", required = true)
    private String mainImageUrl;
    @ApiModelProperty(value = "应用状态", required = true)
    private String status;
    @ApiModelProperty(value = "应用生效时间", required = true)
    private LocalDateTime startTime;
    @ApiModelProperty(value = "应用失效时间", required = true)
    private LocalDateTime endTime;
    @ApiModelProperty(value = "使用说明文档地址", required = false)
    private String useDocUrl;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;
    @ApiModelProperty(value = "更新时间", required = true)
    private LocalDateTime updatedTime;

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getUseDocUrl() {
        return useDocUrl;
    }

    public void setUseDocUrl(String useDocUrl) {
        this.useDocUrl = useDocUrl;
    }

    public String getMainImageUrl() {
        return mainImageUrl;
    }

    public void setMainImageUrl(String mainImageUrl) {
        this.mainImageUrl = mainImageUrl;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}