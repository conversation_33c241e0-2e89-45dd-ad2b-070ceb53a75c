package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "应用VIP定义信息")
public class QueryApplicationAllInfoResponseData {
    @ApiModelProperty(value = "应用信息", required = false)
    private ApplicationInfo applicationInfo;
    @ApiModelProperty(value = "VIP信息", required = false)
    private List<VipInfo> vipInfoList;

    public ApplicationInfo getApplicationInfo() {
        return applicationInfo;
    }

    public void setApplicationInfo(ApplicationInfo queryApplicationResponseData) {
        this.applicationInfo = queryApplicationResponseData;
    }

    public List<VipInfo> getVipInfoList() {
        return vipInfoList;
    }

    public void setVipInfoList(List<VipInfo> vipInfoList) {
        this.vipInfoList = vipInfoList;
    }

    @ApiModel(description = "查询应用定义信息响应对象")
    public static class ApplicationInfo {
        @ApiModelProperty(value = "应用id", required = true)
        private String id;
        @ApiModelProperty(value = "应用编码", required = true)
        private String code;
        @ApiModelProperty(value = "应用名称", required = true)
        private String name;
        @ApiModelProperty(value = "应用描述", required = true)
        private String description;
        @ApiModelProperty(value = "应用主图地址", required = true)
        private String mainImageUrl;
        @ApiModelProperty(value = "应用状态", required = true)
        private String status;
        @ApiModelProperty(value = "应用生效时间", required = true)
        private LocalDateTime startTime;
        @ApiModelProperty(value = "应用失效时间", required = true)
        private LocalDateTime endTime;
        @ApiModelProperty(value = "使用说明文档地址", required = false)
        private String useDocUrl;
        @ApiModelProperty(value = "创建时间", required = true)
        private LocalDateTime createdTime;
        @ApiModelProperty(value = "更新时间", required = true)
        private LocalDateTime updatedTime;

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public void setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
        }

        public String getUseDocUrl() {
            return useDocUrl;
        }

        public void setUseDocUrl(String useDocUrl) {
            this.useDocUrl = useDocUrl;
        }

        public String getMainImageUrl() {
            return mainImageUrl;
        }

        public void setMainImageUrl(String mainImageUrl) {
            this.mainImageUrl = mainImageUrl;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public LocalDateTime getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(LocalDateTime createdTime) {
            this.createdTime = createdTime;
        }

        public LocalDateTime getUpdatedTime() {
            return updatedTime;
        }

        public void setUpdatedTime(LocalDateTime updatedTime) {
            this.updatedTime = updatedTime;
        }
    }

    @ApiModel(description = "VIP定义信息")
    public static class VipInfo {
        @ApiModelProperty(value = "VIP id", required = true)
        private String id;
        @ApiModelProperty(value = "VIP编码", required = true)
        private String code;
        @ApiModelProperty(value = "VIP名称", required = true)
        private String name;
        @ApiModelProperty(value = "VIP说明", required = true)
        private String description;
        @ApiModelProperty(value = "VIP价格信息", required = true)
        private List<VipPriceInfo> priceList;
        @ApiModelProperty(value = "VIP权限信息", required = true)
        private List<VipPermissionInfo> vipPermissionInfoList;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<VipPriceInfo> getPriceList() {
            return priceList;
        }

        public void setPriceList(List<VipPriceInfo> priceList) {
            this.priceList = priceList;
        }

        public List<VipPermissionInfo> getVipPermissionInfoList() {
            return vipPermissionInfoList;
        }

        public void setVipPermissionInfoList(List<VipPermissionInfo> vipPermissionInfoList) {
            this.vipPermissionInfoList = vipPermissionInfoList;
        }

        @ApiModel(description = "VIP价格定义信息")
        public static class VipPriceInfo {
            @ApiModelProperty(value = "VIP价格id", required = true)
            private String vipPriceId;
            @ApiModelProperty(value = "VIP时长", required = true)
            private Integer duration;
            @ApiModelProperty(value = "VIP时长单位", required = true)
            private String durationUnit;
            @ApiModelProperty(value = "VIP价格", required = true)
            private BigDecimal price;
            @ApiModelProperty(value = "VIP积分", required = true)
            private Integer score;
            @ApiModelProperty(value = "购买次数", required = true)
            private Integer buyTimes;

            public Integer getBuyTimes() {
                return buyTimes;
            }

            public void setBuyTimes(Integer buyTimes) {
                this.buyTimes = buyTimes;
            }

            public String getVipPriceId() {
                return vipPriceId;
            }

            public void setVipPriceId(String vipPriceId) {
                this.vipPriceId = vipPriceId;
            }

            public Integer getDuration() {
                return duration;
            }

            public void setDuration(Integer duration) {
                this.duration = duration;
            }

            public String getDurationUnit() {
                return durationUnit;
            }

            public void setDurationUnit(String durationUnit) {
                this.durationUnit = durationUnit;
            }

            public BigDecimal getPrice() {
                return price;
            }

            public void setPrice(BigDecimal price) {
                this.price = price;
            }

            public Integer getScore() {
                return score;
            }

            public void setScore(Integer score) {
                this.score = score;
            }
        }

        @ApiModel(description = "VIP权限定义信息")
        public static class VipPermissionInfo {
            @ApiModelProperty(value = "VIP权限id", required = true)
            private String permissionId;
            @ApiModelProperty(value = "资源id", required = true)
            private String resourceId;
            @ApiModelProperty(value = "资源名称", required = true)
            private String resourceName;
            @ApiModelProperty(value = "每次使用的积分", required = true)
            private Integer eachUseScore;

            public Integer getEachUseScore() {
                return eachUseScore;
            }

            public void setEachUseScore(Integer eachUseScore) {
                this.eachUseScore = eachUseScore;
            }

            public String getResourceId() {
                return resourceId;
            }

            public void setResourceId(String resourceId) {
                this.resourceId = resourceId;
            }


            public String getPermissionId() {
                return permissionId;
            }

            public void setPermissionId(String permissionId) {
                this.permissionId = permissionId;
            }

            public String getResourceName() {
                return resourceName;
            }

            public void setResourceName(String resourceName) {
                this.resourceName = resourceName;
            }
        }
    }
}