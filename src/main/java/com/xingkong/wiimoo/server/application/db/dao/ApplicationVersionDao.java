package com.xingkong.wiimoo.server.application.db.dao;

import com.mybatisflex.core.BaseMapper;
import com.xingkong.wiimoo.server.application.db.entity.ApplicationVersionEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ApplicationVersionDao extends BaseMapper<ApplicationVersionEntity> {

//    @Select("select * from application_history_version ${ew.customSqlSegment}")
//    List<GetLatestVersionResponseData> selectListByMapper(@Param( Constants.WRAPPER) LambdaQueryWrapper<ApplicationHistoryVersionEntity> queryWrapper);
//
//    @Select("select * from application_history_version ${ew.customSqlSegment}")
//    Page<GetApplicationVersionResponseData> selectApplicationVersionByMapper(Page<GetApplicationVersionResponseData> page,@Param(Constants.WRAPPER) LambdaQueryWrapper<ApplicationHistoryVersionEntity> queryWrapper);
}