package com.xingkong.wiimoo.server.application.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.application.service.PresetService;
import com.xingkong.wiimoo.server.application.web.dto.CreatePresetRequest;
import com.xingkong.wiimoo.server.application.web.dto.CreatePresetResopnseData;
import com.xingkong.wiimoo.server.application.web.dto.QueryPresetResponseData;
import com.xingkong.wiimoo.server.application.web.dto.UpdatePresetRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "预设服务接口")
@RestController
@RequestMapping("/preset")
public class PresetController {

    @Autowired
    private PresetService presetService;

    @ApiOperation(value = "创建预设接口",notes = "创建预设")
    @PostMapping("/{applicationCode}/create")
    @ResponseBody
    public Result<CreatePresetResopnseData> createPreset(@PathVariable("applicationCode") String applicationCode,
                                                 @RequestBody CreatePresetRequest createPresetRequest) {
        if (!StpKit.getStpLogic(applicationCode).isLogin()) {
            return Result.authenticationError("未登录");
        }
        return  presetService.createPreset(applicationCode, createPresetRequest);
    }

    @ApiOperation(value = "更新预设接口",notes = "更新预设")
    @PostMapping("/update")
    @ResponseBody
    public Result updatePreset(@RequestBody UpdatePresetRequest updatePresetRequest) {
        return presetService.updatePreset(updatePresetRequest);
    }

    @ApiOperation(value = "删除预设接口",notes = "删除预设")
    @GetMapping("/delete")
    @ResponseBody
    public Result deletePreset(@RequestParam("id") String id) {
        return presetService.deletePreset(id);
    }

    @ApiOperation(value = "查询预设接口",notes = "查询预设")
    @GetMapping("/{applicationCode}/query")
    @ResponseBody
    public Result<List<QueryPresetResponseData>> queryPreset( @PathVariable("applicationCode") String applicationCode) {
        return presetService.queryPreset(applicationCode);
    }
}