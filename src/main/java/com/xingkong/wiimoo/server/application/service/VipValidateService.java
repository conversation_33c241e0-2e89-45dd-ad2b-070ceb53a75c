package com.xingkong.wiimoo.server.application.service;

import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.server.application.db.entity.VipEntity;
import com.xingkong.wiimoo.server.application.db.entity.VipPriceEntity;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

import static com.xingkong.wiimoo.server.application.db.entity.table.VipPriceTableDef.VipPrice;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipTableDef.Vip;

@Service
@Validated
public class VipValidateService {

    public Result isValid(@NotBlank(message = "vip价格id不能为空") String vipPriceId, @NotNull(message = "vip价格不能为空") @Min(value = 0, message = "vip价格不能小于0") BigDecimal price) {
        VipPriceEntity vipPriceEntity =  QueryChain.of(VipPriceEntity.class).select()
                .from(VipPriceEntity.class)
                .where(VipPrice.Id.eq(vipPriceId))
                .one();
        if (vipPriceEntity == null) {
            return Result.businessError("vip价格不存在");
        }
        if(vipPriceEntity.getPrice().compareTo(price)!=0){
            return Result.businessError("vip价格错误");
        }
        if (vipPriceEntity.getEndTime().isBefore(vipPriceEntity.getStartTime())) {
            return Result.businessError("vip价格已停用");
        }
        String vipId = vipPriceEntity.getVipId();
        VipEntity vipEntity =   QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Id.eq(vipId))
                .one();
        if (vipEntity == null) {
            return Result.businessError("vip不存在");
        }
        if (vipEntity.getEndTime().isBefore(vipEntity.getStartTime())) {
            return Result.businessError(vipEntity.getName() + "已停用");
        }
        Result  result = isValid(vipPriceId);
        if(!ResultCode.SUCCESS.getCode().equals(result.getResultCode())){
            return result;
        }
        return Result.success();
    }

    public Result isValid(@NotBlank(message = "vip价格id不能为空") String vipPriceId) {
        VipPriceEntity vipPriceEntity =   QueryChain.of(VipPriceEntity.class).select()
                .from(VipPriceEntity.class)
                .where(VipPrice.Id.eq(vipPriceId))
                .one();
        if (vipPriceEntity == null) {
            return Result.businessError("vip价格不存在");
        }
        if (vipPriceEntity.getEndTime().isBefore(vipPriceEntity.getStartTime())) {
            return Result.businessError("vip价格已停用");
        }
        String vipId = vipPriceEntity.getVipId();
        VipEntity vipEntity =  QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Id.eq(vipId))
                .one();
        if (vipEntity == null) {
            return Result.businessError("vip不存在");
        }
        if (vipEntity.getEndTime().isBefore(vipEntity.getStartTime())) {
            return Result.businessError(vipEntity.getName() + "已停用");
        }
        return Result.success();
    }
}