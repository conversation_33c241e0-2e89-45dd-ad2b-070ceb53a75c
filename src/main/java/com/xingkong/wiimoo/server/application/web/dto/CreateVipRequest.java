package com.xingkong.wiimoo.server.application.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel(description = "创建VIP请求对象")
public class CreateVipRequest {
    @ApiModelProperty(value = "应用编码", required = true)
    @NotBlank(message = "应用编码不能为空")
    private String applicationCode;
    @ApiModelProperty(value = "VIP编码", required = true)
    @NotBlank(message = "VIP编码不能为空")
    private String code;
    @ApiModelProperty(value = "VIP名称", required = true)
    @NotBlank(message = "VIP名称不能为空")
    private String name;
    @ApiModelProperty(value = "VIP说明", required = true)
    @NotBlank(message = "VIP说明不能为空")
    private String description;
    @ApiModelProperty(value = "VIP生效时间", required = true)
    @NotNull(message = "VIP生效时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "VIP失效时间", required = true)
    @NotNull(message = "VIP失效时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}