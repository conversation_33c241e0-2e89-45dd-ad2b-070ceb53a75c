package com.xingkong.wiimoo.server.application.web.dto;

import com.xingkong.wiimoo.common.support.MyPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("获取应用版本信息请求")
public class QueryApplicationVersionPageRequest {
    @ApiModelProperty(value = "分页信息", required = true)
    @NotNull(message = "分页信息不能为空")
    @Valid
    private MyPage page;
    @ApiModelProperty(value = "应用编码", required = false)
    private String applicationCode;
    @ApiModelProperty(value = "应用状态", required = false)
    private List<String> applicationStatusList;
    @ApiModelProperty(value = "版本号", required = false)
    private String version;
    @ApiModelProperty(value = "版本状态", required = false)
    private List<String> versionStatusList;

    public List<String> getApplicationStatusList() {
        return applicationStatusList;
    }

    public void setApplicationStatusList(List<String> applicationStatusList) {
        this.applicationStatusList = applicationStatusList;
    }

    public MyPage getPage() {
        return page;
    }

    public void setPage(MyPage page) {
        this.page = page;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<String> getVersionStatusList() {
        return versionStatusList;
    }

    public void setVersionStatusList(List<String> versionStatusList) {
        this.versionStatusList = versionStatusList;
    }
}