package com.xingkong.wiimoo.server.application.web.controller;

import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.application.service.ResourceQueryService;
import com.xingkong.wiimoo.server.application.service.ResourceService;
import com.xingkong.wiimoo.server.application.web.dto.CreateResourceRequest;
import com.xingkong.wiimoo.server.application.web.dto.QueryResourceResponseData;
import com.xingkong.wiimoo.server.application.web.dto.UpdateResourceRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "资源服务接口")
@RestController
@RequestMapping("/resource")
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private ResourceQueryService resourceQueryService;

    @ApiOperation(value = "创建资源接口",notes = "创建资源")
    @PostMapping("/create")
    @ResponseBody
    public Result createResource(@RequestBody CreateResourceRequest createResourceRequest) {
        return resourceService.createResource(createResourceRequest);
    }

    @ApiOperation(value = "更新资源接口",notes = "更新资源")
    @PostMapping("/update")
    @ResponseBody
    public Result updateResource(@RequestBody UpdateResourceRequest updateResourceRequest) {
        return resourceService.updateResource(updateResourceRequest);
    }

    @ApiOperation(value = "启用资源接口",notes = "启用资源")
    @GetMapping("/enable")
    @ResponseBody
    public Result enableResource(@RequestParam(value = "resourceId", required = true) String resourceId) {
        return resourceService.enableResource(resourceId);
    }

    @ApiOperation(value = "停用资源接口",notes = "停用资源")
    @GetMapping("/disable")
    @ResponseBody
    public Result disableResource(@RequestParam(value = "resourceId", required = true) String resourceId) {
        return resourceService.disableResource(resourceId);
    }

    @ApiOperation(value = "查询资源接口",notes = "查询资源")
    @GetMapping("/query")
    @ResponseBody
    public Result<List<QueryResourceResponseData>> queryResource(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return resourceQueryService.queryResource(applicationCode);
    }
}