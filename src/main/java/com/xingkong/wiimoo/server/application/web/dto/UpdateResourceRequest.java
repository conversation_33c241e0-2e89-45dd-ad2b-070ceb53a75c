package com.xingkong.wiimoo.server.application.web.dto;

import com.xingkong.base.infrastructure.ability.validation.EnumValidation;
import com.xingkong.wiimoo.server.application.support.ResourceType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel(description = "更新资源请求对象")
public class UpdateResourceRequest {
    @ApiModelProperty(value = "资源id", required = true)
    @NotBlank(message = "资源id不能为空")
    private String id;
    @ApiModelProperty(value = "应用编码", required = true)
    @NotBlank(message = "应用编码不能为空")
    private String applicationCode;
    @ApiModelProperty(value = "资源编码", required = true)
    @NotBlank(message = "资源编码不能为空")
    private String code;
    @ApiModelProperty(value = "资源名称", required = true)
    @NotBlank(message = "资源名称不能为空")
    private String name;
    @ApiModelProperty(value = "资源说明", required = true)
    @NotBlank(message = "资源说明不能为空")
    private String description;
    @ApiModelProperty(value = "资源类型", required = true)
    @NotBlank(message = "资源类型不能为空")
    @EnumValidation(enumClass = ResourceType.class, message = "不合法的资源类型")
    private String type;
    @ApiModelProperty(value = "每次使用的积分", required = true)
    @NotNull(message = "每次使用的积分不能为空")
    @Min(value = 0, message = "每次使用的积分不能小于0")
    private Integer eachUseScore;
    @ApiModelProperty(value = "资源生效时间", required = true)
    @NotNull(message = "资源生效时间不能为空")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "资源失效时间", required = true)
    @NotNull(message = "资源失效时间不能为空")
    private LocalDateTime endTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getEachUseScore() {
        return eachUseScore;
    }

    public void setEachUseScore(Integer eachUseScore) {
        this.eachUseScore = eachUseScore;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}