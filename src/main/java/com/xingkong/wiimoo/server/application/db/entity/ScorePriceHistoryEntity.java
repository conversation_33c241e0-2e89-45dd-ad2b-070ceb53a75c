package com.xingkong.wiimoo.server.application.db.entity;


import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Table(value = "score_price_history")
public class ScorePriceHistoryEntity {

  private String id;
  private String scorePriceId;
  private Integer unitQuantity;
  private BigDecimal price;
  private String status;
  private LocalDateTime startTime;
  private LocalDateTime endTime;
  private LocalDateTime createdTime;

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public String getScorePriceId() {
    return scorePriceId;
  }

  public void setScorePriceId(String scorePriceId) {
    this.scorePriceId = scorePriceId;
  }



  public Integer getUnitQuantity() {
    return unitQuantity;
  }

  public void setUnitQuantity(Integer unitQuantity) {
    this.unitQuantity = unitQuantity;
  }


  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }


  public LocalDateTime getStartTime() {
    return startTime;
  }

  public void setStartTime(LocalDateTime startTime) {
    this.startTime = startTime;
  }


  public LocalDateTime getEndTime() {
    return endTime;
  }

  public void setEndTime(LocalDateTime endTime) {
    this.endTime = endTime;
  }


  public LocalDateTime getCreatedTime() {
    return createdTime;
  }

  public void setCreatedTime(LocalDateTime createdTime) {
    this.createdTime = createdTime;
  }

}