package com.xingkong.wiimoo.server.application.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.mybatisflex.core.paginate.Page;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.application.service.ApplicationQueryService;
import com.xingkong.wiimoo.server.application.service.ApplicationService;
import com.xingkong.wiimoo.server.application.web.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "应用服务接口")
@RestController
@RequestMapping("/application")
public class ApplicationController {

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private ApplicationQueryService applicationQueryService;


    @ApiOperation(value = "创建应用接口", notes = "创建应用")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @PostMapping("/create")
    @ResponseBody
    public Result createApplication(@RequestBody CreateApplicationRequest createApplicationRequest) {
        return applicationService.createApplication(createApplicationRequest);
    }

    @ApiOperation(value = "更新应用接口", notes = "更新应用")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @PostMapping("/update")
    @ResponseBody
    public Result updateApplication(@RequestBody UpdateApplicationRequest updateApplicationRequest) {
        return applicationService.updateApplication(updateApplicationRequest);
    }

    @ApiOperation(value = "启用应用接口", notes = "启用应用")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @GetMapping("/enable")
    @ResponseBody
    public Result enableApplication(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return applicationService.enableApplication(applicationCode);
    }

    @ApiOperation(value = "停用应用接口", notes = "停用应用")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @GetMapping("/disable")
    @ResponseBody
    public Result disableApplication(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return applicationService.disableApplication(applicationCode);
    }

    @ApiOperation(value = "分页查询应用列表接口", notes = "分页查询应用列表")
    @PostMapping("/query/page")
    @ResponseBody
    public Result<Page<QueryApplicationResponseData>> queryApplicationPage(@RequestBody QueryApplicationPageRequest queryApplicationPageRequest) {

        return applicationQueryService.queryApplicationPage(queryApplicationPageRequest);
    }

    @ApiOperation(value = "查询应用信息接口", notes = "查询应用信息")
    @GetMapping("/query")
    @ResponseBody
    public Result<QueryApplicationResponseData> queryApplication(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return applicationQueryService.queryApplication(applicationCode);
    }

    @ApiOperation(value = "获取应用收费功能信息接口", notes = "获取应用收费功能信息接口")
    @GetMapping("/functions/query")
    @ResponseBody
    public Result<List<QueryApplicationFunctionResponseData>> queryApplicationFunctions(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return applicationQueryService.queryApplicationFunctions(applicationCode);
    }

    @ApiOperation(value = "创建应用版本接口", notes = "创建应用版本")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @PostMapping("/version/create")
    @ResponseBody
    public Result createApplicationVersion(@RequestBody CreateApplicationVersionRequest createApplicationVersionRequest) {
        return applicationService.createApplicationVersion(createApplicationVersionRequest);
    }
    @ApiOperation(value = "更新应用版本接口", notes = "更新应用版本")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @PostMapping("/version/update")
    @ResponseBody
    public Result updateApplicationVersion(@RequestBody UpdateApplicationVersionRequest updateApplicationVersionRequest) {
        return applicationService.updateApplicationVersion(updateApplicationVersionRequest);
    }

    @ApiOperation(value = "发布应用版本接口", notes = "发布应用版本")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @PostMapping("/version/publish")
    @ResponseBody
    public Result publishApplicationVersion(@RequestParam("applicationVersionId") String applicationVersionId) {
        return applicationService.publishApplicationVersion(applicationVersionId);
    }
    @ApiOperation(value = "停用应用版本接口", notes = "停用应用版本")
    @SaCheckLogin(type = StpKit.ADMIN_TYPE)
    @PostMapping("/version/disable")
    @ResponseBody
    public Result disableApplicationVersion(@RequestParam("applicationVersionId") String applicationVersionId) {
        return applicationService.disableApplicationVersion(applicationVersionId);
    }


    @ApiOperation(value = "获取应用最新版本接口", notes = "获取应用最新版本接口")
    @GetMapping("/version/query/latest")
    @ResponseBody
    public Result<QueryVersionResponseData> queryLatestVersion(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return applicationQueryService.queryLatestVersion(applicationCode);
    }

    @ApiOperation(value = "检查版本更新接口", notes = "检查版本更新")
    @GetMapping("/version/check")
    @ResponseBody
    public Result<CheckVersionResponseData> checkVersion(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode,
                                                         @ApiParam(value = "版本号", required = true) @RequestParam("version") String version) {
        return applicationQueryService.checkVersion(applicationCode, version);
    }

    @ApiOperation(value = "分页查询应用版本信息接口", notes = "分页查询应用版本信息")
    @PostMapping("/version/query/page")
    @ResponseBody
    public Result<Page<QueryApplicationVersionResponseData>> queryApplicationVersionPage(@RequestBody QueryApplicationVersionPageRequest queryApplicationPageRequest) {
        return applicationQueryService.queryApplicationVersionPage(queryApplicationPageRequest);
    }


    @ApiOperation(value = "获取应用全量定义信息接口", notes = "获取应用的全量定义信息，包括说明，VIP，定价，权限等信息")
    @GetMapping("/query/all")
    @ResponseBody
    public Result<QueryApplicationAllInfoResponseData> queryApplicationAllInfo(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return applicationQueryService.queryApplicationAllInfo(applicationCode);
    }
}