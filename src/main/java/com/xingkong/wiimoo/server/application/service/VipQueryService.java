package com.xingkong.wiimoo.server.application.service;

import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.common.support.VipPermissionStatusEnum;
import com.xingkong.wiimoo.server.application.db.entity.*;
import com.xingkong.wiimoo.server.application.web.dto.QueryMyVipResponseData;
import com.xingkong.wiimoo.server.application.web.dto.QueryVipPermissionResponseData;
import com.xingkong.wiimoo.server.application.web.dto.QueryVipPriceResponseData;
import com.xingkong.wiimoo.server.application.web.dto.QueryVipResponseData;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerVipEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationTableDef.Application;
import static com.xingkong.wiimoo.server.application.db.entity.table.ResourceTableDef.Resource;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipPermissionTableDef.VipPermission;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipPriceTableDef.VipPrice;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipTableDef.Vip;
import static com.xingkong.wiimoo.server.consumer.db.entity.table.ConsumerVipTableDef.ConsumerVip;

@Service
@Validated
public class VipQueryService {

    public Result<List<QueryMyVipResponseData>> myVipInfos() {
        String consumerId = StpKit.STORE.getLoginIdAsString();
        return queryVipInfos(consumerId);
    }

    public String myVipInfo(String applicationCode) {

        return null;
    }

    public Result<List<QueryMyVipResponseData>> queryVipInfos(@NotBlank(message = "用户id不能为空") String consumerId) {

        List<QueryMyVipResponseData> queryMyVipResponseDataList = QueryChain.of(ConsumerVipEntity.class).select(
                        Application.Code.as(QueryMyVipResponseData::getApplicationCode),
                        Application.Name.as(QueryMyVipResponseData::getApplicationName),
                        Vip.Code.as(QueryMyVipResponseData::getVipCode),
                        Vip.Name.as(QueryMyVipResponseData::getVipName),
                        ConsumerVip.StartTime.as(QueryMyVipResponseData::getVipStartTime),
                        ConsumerVip.EndTime.as(QueryMyVipResponseData::getVipEndTime)
                )
                .from(ConsumerVipEntity.class)
                .leftJoin(VipEntity.class).on(ConsumerVipEntity::getVipId, VipEntity::getId)
                .leftJoin(ApplicationEntity.class).on(VipEntity::getApplicationCode, ApplicationEntity::getCode)
                .where(ConsumerVipEntity::getConsumerId).eq(consumerId)
                .listAs(QueryMyVipResponseData.class);
        return Result.success(queryMyVipResponseDataList);
    }

    public Result<List<QueryVipResponseData>> queryVip(@NotBlank(message = "应用编码不能为空") String applicationCode) {
        List<QueryVipResponseData> queryVipResponseDataList = QueryChain.of(VipEntity.class).select(
                        Vip.Id.as(QueryVipResponseData::getVipId),
                        Vip.ApplicationCode.as(QueryVipResponseData::getApplicationCode),
                        Application.Name.as(QueryVipResponseData::getApplicationName),
                        Application.Status.as(QueryVipResponseData::getApplicationStatus),
                        Vip.Code.as(QueryVipResponseData::getVipCode),
                        Vip.Name.as(QueryVipResponseData::getVipName),
                        Vip.Description.as(QueryVipResponseData::getDescription),
                        Vip.Status.as(QueryVipResponseData::getVipStatus),
                        Vip.StartTime.as(QueryVipResponseData::getStartTime),
                        Vip.EndTime.as(QueryVipResponseData::getEndTime),
                        Vip.CreatedTime.as(QueryVipResponseData::getCreatedTime),
                        Vip.UpdatedTime.as(QueryVipResponseData::getUpdatedTime)
                )
                .from(VipEntity.class)
                .leftJoin(ApplicationEntity.class).on(VipEntity::getApplicationCode, ApplicationEntity::getCode)
                .where(Vip.ApplicationCode.eq(applicationCode))
                .listAs(QueryVipResponseData.class);
        return Result.success(queryVipResponseDataList);
    }

    public Result<List<QueryVipPriceResponseData>> queryVipPriceList(@NotBlank(message = "应用编码不能为空") String applicationCode, String vipId) {
        List<QueryVipPriceResponseData> queryVipPriceResponseDataList = QueryChain.of(VipPriceEntity.class).select(
                        VipPrice.Id.as(QueryVipPriceResponseData::getVipPriceId),
                        Vip.ApplicationCode.as(QueryVipPriceResponseData::getApplicationCode),
                        Application.Name.as(QueryVipPriceResponseData::getApplicationName),
                        Application.Status.as(QueryVipPriceResponseData::getApplicationStatus),
                        Vip.Id.as(QueryVipPriceResponseData::getVipId),
                        Vip.Code.as(QueryVipPriceResponseData::getVipCode),
                        Vip.Name.as(QueryVipPriceResponseData::getVipName),
                        VipPrice.Price.as(QueryVipPriceResponseData::getPrice),
                        VipPrice.Duration.as(QueryVipPriceResponseData::getDuration),
                        VipPrice.DurationUnit.as(QueryVipPriceResponseData::getDurationUnit),
                        VipPrice.Status.as(QueryVipPriceResponseData::getPriceStatus),
                        VipPrice.StartTime.as(QueryVipPriceResponseData::getStartTime),
                        VipPrice.EndTime.as(QueryVipPriceResponseData::getEndTime),
                        VipPrice.CreatedTime.as(QueryVipPriceResponseData::getCreatedTime),
                        VipPrice.UpdatedTime.as(QueryVipPriceResponseData::getUpdatedTime),
                        VipPrice.Score.as(QueryVipPriceResponseData::getScore),
                        VipPrice.BuyTimes.as(QueryVipPriceResponseData::getBuyTimes)
                )
                .from(VipPriceEntity.class)
                .leftJoin(VipEntity.class).on(VipPriceEntity::getVipId, VipEntity::getId)
                .leftJoin(ApplicationEntity.class).on(VipEntity::getApplicationCode, ApplicationEntity::getCode)
                .where(Vip.ApplicationCode.eq(applicationCode)).and(Vip.Id.eq(vipId).when(!StringUtils.isEmpty(vipId)))
                .listAs(QueryVipPriceResponseData.class);
        return Result.success(queryVipPriceResponseDataList);
    }

    public Result<List<QueryVipPermissionResponseData>> queryVipPermissionList(@NotBlank(message = "应用编码不能为空") String applicationCode, String vipId) {
        List<QueryVipPermissionResponseData> queryVipPermissionResponseDataList = QueryChain.of(VipPermissionEntity.class).select(
                        VipPermission.Id.as(QueryVipPermissionResponseData::getId),
                        VipPermission.VipId.as(QueryVipPermissionResponseData::getVipId),
                        VipPermission.ResourceId.as(QueryVipPermissionResponseData::getResourceId),
                        Resource.Name.as(QueryVipPermissionResponseData::getResourceName),
                        Resource.EachUseScore.as(QueryVipPermissionResponseData::getEachUseScore),
                        VipPermission.CreatedTime.as(QueryVipPermissionResponseData::getCreatedTime),
                        VipPermission.UpdatedTime.as(QueryVipPermissionResponseData::getUpdatedTime)
                )
                .from(VipPermissionEntity.class)
                .leftJoin(ResourceEntity.class).on(VipPermissionEntity::getResourceId, ResourceEntity::getId)
                .where(Resource.ApplicationCode.eq(applicationCode))
                .and(VipPermission.VipId.eq(vipId).when(!StringUtils.isEmpty(vipId)))
                .and(VipPermission.Status.eq(VipPermissionStatusEnum.ACTIVE.getCode()))
                .listAs(QueryVipPermissionResponseData.class);
        return Result.success(queryVipPermissionResponseDataList);
    }

}