package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel(description = "更新VIP请求对象")
public class UpdateVipRequest {
    @ApiModelProperty(value = "VIP id", required = true)
    @NotBlank(message = "VIP id不能为空")
    private String id;
    @ApiModelProperty(value = "VIP名称", required = true)
    @NotBlank(message = "VIP名称不能为空")
    private String name;
    @ApiModelProperty(value = "VIP描述", required = true)
    @NotBlank(message = "VIP描述不能为空")
    private String description;
    @ApiModelProperty(value = "VIP生效时间", required = true)
    @NotNull(message = "VIP生效时间不能为空")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "VIP失效时间", required = true)
    @NotNull(message = "VIP失效时间不能为空")
    private LocalDateTime endTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}