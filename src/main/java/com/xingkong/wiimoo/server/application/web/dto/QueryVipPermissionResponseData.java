package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel(description = "查询VIP权限信息响应对象")
public class QueryVipPermissionResponseData {

    @ApiModelProperty(value = "VIP权限id", required = true)
    private String id;
    @ApiModelProperty(value = "VIP id", required = true)
    private String vipId;
    @ApiModelProperty(value = "资源id", required = true)
    private String resourceId;
    @ApiModelProperty(value = "资源名称", required = true)
    private String resourceName;
    @ApiModelProperty(value = "每次使用的积分", required = true)
    private Integer eachUseScore;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;
    @ApiModelProperty(value = "更新时间", required = true)
    private LocalDateTime updatedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public Integer getEachUseScore() {
        return eachUseScore;
    }

    public void setEachUseScore(Integer eachUseScore) {
        this.eachUseScore = eachUseScore;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}