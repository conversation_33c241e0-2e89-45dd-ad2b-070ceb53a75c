package com.xingkong.wiimoo.server.application.web.dto;

import com.xingkong.wiimoo.common.support.MyPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "分页查询VIP价格请求对象")
public class QueryVipPricePageRequest {
    @ApiModelProperty(value = "分页信息", required = true)
    @NotNull(message = "分页信息不能为空")
    @Valid
    private MyPage page;
    @ApiModelProperty(value = "应用编码", required = false)
    private String applicationCode;
    @ApiModelProperty(value = "应用状态", required = false)
    private List<String> applicationStatusList;
    @ApiModelProperty(value = "VIP编码", required = false)
    private String vipCode;
    @ApiModelProperty(value = "VIP状态", required = false)
    private List<String> vipStatusList;
    @ApiModelProperty(value = "VIP价格状态", required = false)
    private List<String> priceStatusList;

    public MyPage getPage() {
        return page;
    }

    public void setPage(MyPage page) {
        this.page = page;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public List<String> getApplicationStatusList() {
        return applicationStatusList;
    }

    public void setApplicationStatusList(List<String> applicationStatusList) {
        this.applicationStatusList = applicationStatusList;
    }

    public String getVipCode() {
        return vipCode;
    }

    public void setVipCode(String vipCode) {
        this.vipCode = vipCode;
    }

    public List<String> getVipStatusList() {
        return vipStatusList;
    }

    public void setVipStatusList(List<String> vipStatusList) {
        this.vipStatusList = vipStatusList;
    }

    public List<String> getPriceStatusList() {
        return priceStatusList;
    }

    public void setPriceStatusList(List<String> priceStatusList) {
        this.priceStatusList = priceStatusList;
    }
}