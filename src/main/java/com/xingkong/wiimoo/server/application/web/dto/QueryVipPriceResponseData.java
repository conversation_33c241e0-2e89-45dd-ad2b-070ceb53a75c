package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "查询VIP价格信息响应对象")
public class QueryVipPriceResponseData {
    @ApiModelProperty(value = "VIP价格id", required = true)
    private String vipPriceId;
    @ApiModelProperty(value = "应用编码", required = true)
    private String applicationCode;
    @ApiModelProperty(value = "应用名称", required = true)
    private String applicationName;
    @ApiModelProperty(value = "应用状态", required = true)
    private String applicationStatus;
    @ApiModelProperty(value = "VIP id", required = true)
    private String vipId;
    @ApiModelProperty(value = "VIP编码", required = true)
    private String vipCode;
    @ApiModelProperty(value = "VIP名称", required = true)
    private String vipName;
    @ApiModelProperty(value = "VIP价格", required = true)
    private BigDecimal price;
    @ApiModelProperty(value = "VIP时长", required = true)
    private Integer duration;
    @ApiModelProperty(value = "VIP时长单位", required = true)
    private String durationUnit;
    @ApiModelProperty(value = "VIP价格状态", required = true)
    private String priceStatus;
    @ApiModelProperty(value = "VIP价格生效时间", required = true)
    private LocalDateTime startTime;
    @ApiModelProperty(value = "VIP价格失效时间", required = true)
    private LocalDateTime endTime;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;
    @ApiModelProperty(value = "更新时间", required = true)
    private LocalDateTime updatedTime;
    @ApiModelProperty(value = "VIP积分", required = true)
    private Integer score;
    @ApiModelProperty(value = "购买次数", required = true)
    private Integer buyTimes;

    public Integer getBuyTimes() {
        return buyTimes;
    }

    public void setBuyTimes(Integer buyTimes) {
        this.buyTimes = buyTimes;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(String applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public String getVipPriceId() {
        return vipPriceId;
    }

    public void setVipPriceId(String vipPriceId) {
        this.vipPriceId = vipPriceId;
    }

    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public String getVipCode() {
        return vipCode;
    }

    public void setVipCode(String vipCode) {
        this.vipCode = vipCode;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getDurationUnit() {
        return durationUnit;
    }

    public void setDurationUnit(String durationUnit) {
        this.durationUnit = durationUnit;
    }

    public String getPriceStatus() {
        return priceStatus;
    }

    public void setPriceStatus(String priceStatus) {
        this.priceStatus = priceStatus;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }


}