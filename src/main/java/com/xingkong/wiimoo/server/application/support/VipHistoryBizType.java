package com.xingkong.wiimoo.server.application.support;

public enum VipHistoryBizType {
    BUY("BUY", "购买"),
    TRIAL("TRIAL", "试用");
    private String code;
    private String desc;

    VipHistoryBizType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}