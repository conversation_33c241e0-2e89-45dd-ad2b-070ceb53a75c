package com.xingkong.wiimoo.server.application.service;

import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.support.ApplicationVersionStatusEnum;
import com.xingkong.wiimoo.common.support.ApplicationStatusEnum;
import com.xingkong.wiimoo.server.application.db.entity.ApplicationEntity;
import com.xingkong.wiimoo.server.application.db.entity.ApplicationVersionDownloadEntity;
import com.xingkong.wiimoo.server.application.db.entity.ApplicationVersionEntity;
import com.xingkong.wiimoo.server.application.web.dto.CreateApplicationRequest;
import com.xingkong.wiimoo.server.application.web.dto.CreateApplicationVersionRequest;
import com.xingkong.wiimoo.server.application.web.dto.UpdateApplicationRequest;
import com.xingkong.wiimoo.server.application.web.dto.UpdateApplicationVersionRequest;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationTableDef.Application;
import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationVersionDownloadTableDef.ApplicationVersionDownload;
import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationVersionTableDef.ApplicationVersion;


@Service
@Validated
public class ApplicationService {

    public Result createApplication(@Valid CreateApplicationRequest createApplicationRequest) {

        ApplicationEntity applicationEntity = QueryChain.of(ApplicationEntity.class ).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(createApplicationRequest.getCode()))
                .one();

        if(applicationEntity!=null){
            return Result.businessError("应用编码已存在");
        }

        applicationEntity = new ApplicationEntity();
        applicationEntity.setId(UlidCreator.getUlid().toString());
        applicationEntity.setCode(createApplicationRequest.getCode());
        applicationEntity.setName(createApplicationRequest.getName());
        applicationEntity.setDescription(createApplicationRequest.getDescription());
        applicationEntity.setMainImageUrl(createApplicationRequest.getMainImageUrl());
        applicationEntity.setUseDocUrl(createApplicationRequest.getUseDocUrl());
        applicationEntity.setStatus(ApplicationStatusEnum.WAITING.getCode());
        applicationEntity.setStartTime(createApplicationRequest.getStartTime());
        applicationEntity.setEndTime(createApplicationRequest.getEndTime());
        applicationEntity.setCreatedTime(LocalDateTime.now());
        applicationEntity.setUpdatedTime(LocalDateTime.now());

        DbChain.table(ApplicationEntity.class).save(applicationEntity);

        return Result.success();
    }

    public Result updateApplication(@Valid UpdateApplicationRequest updateApplicationRequest) {

        ApplicationEntity applicationEntity = QueryChain.of(ApplicationEntity.class ).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(updateApplicationRequest.getCode()))
                .one();

        if(applicationEntity==null){
            return Result.businessError("应用不存在");
        }
        UpdateChain.of(ApplicationEntity.class)
                .set(Application.Name,updateApplicationRequest.getName())
                .set(Application.Description,updateApplicationRequest.getDescription())
                .set(Application.MainImageUrl,updateApplicationRequest.getMainImageUrl())
                .set(Application.UseDocUrl,updateApplicationRequest.getUseDocUrl())
                .set(Application.StartTime,updateApplicationRequest.getStartTime())
                .set(Application.EndTime,updateApplicationRequest.getEndTime())
                .set(Application.UpdatedTime,LocalDateTime.now())
                .set(Application.Status,ApplicationStatusEnum.WAITING.getCode(),ApplicationStatusEnum.ENABLE.getCode().equals(applicationEntity.getStatus()))
                .where(Application.Code.eq(updateApplicationRequest.getCode()))
                .update();

        return Result.success();
    }

    public Result enableApplication(@NotBlank(message = "应用编码不能为空") String applicationCode) {
        ApplicationEntity applicationEntity = QueryChain.of(ApplicationEntity.class ).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(applicationCode))
                .one();
        if(applicationEntity==null){
            return Result.businessError("应用不存在");
        }
        if(ApplicationStatusEnum.ENABLE.getCode().equals(applicationEntity.getStatus())){
            return Result.success("应用已启用");
        }
        // 检查是否有版本
        ApplicationVersionEntity applicationVersionEntity = QueryChain.of(ApplicationVersionEntity.class ).select()
                .from(ApplicationVersionEntity.class)
                .where(ApplicationVersion.ApplicationCode.eq(applicationCode))
                .and(ApplicationVersion.ReleaseTime.le(LocalDateTime.now()))
                .one();
        if(applicationVersionEntity ==null){
            return Result.businessError("应用没有版本");
        }
        UpdateChain.of(ApplicationEntity.class)
                .set(Application.Status, ApplicationStatusEnum.ENABLE.getCode())
                .set(Application.UpdatedTime, LocalDateTime.now())
                .where(Application.Code.eq(applicationCode))
                .update();
        return Result.success();
    }

    public Result disableApplication(@NotBlank(message = "应用编码不能为空") String applicationCode) {
        ApplicationEntity applicationEntity = QueryChain.of(ApplicationEntity.class ).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(applicationCode))
                .one();
        if(applicationEntity==null){
            return Result.businessError("应用不存在");
        }
        if(ApplicationStatusEnum.DISABLE.getCode().equals(applicationEntity.getStatus())){
            return Result.success("应用已停用");
        }

        UpdateChain.of(ApplicationEntity.class)
                .set(Application.Status, ApplicationStatusEnum.DISABLE.getCode())
                .set(Application.UpdatedTime, LocalDateTime.now())
                .where(Application.Code.eq(applicationCode))
                .update();
        return Result.success();
    }

    public Result createApplicationVersion(@Valid CreateApplicationVersionRequest createApplicationVersionRequest) {
        ApplicationEntity applicationEntity = QueryChain.of(ApplicationEntity.class ).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(createApplicationVersionRequest.getApplicationCode()))
                .one();
        if(applicationEntity==null){
            return Result.businessError("应用不存在");
        }
        ApplicationVersionEntity applicationVersionEntity = new ApplicationVersionEntity();
        applicationVersionEntity.setId(UlidCreator.getUlid().toString());
        applicationVersionEntity.setApplicationCode(createApplicationVersionRequest.getApplicationCode());
        applicationVersionEntity.setVersion(createApplicationVersionRequest.getVersion());
        applicationVersionEntity.setReleaseTime(createApplicationVersionRequest.getReleaseTime());
        applicationVersionEntity.setNotes(createApplicationVersionRequest.getNotes());
        applicationVersionEntity.setStatus(ApplicationVersionStatusEnum.WAITING.getCode());
        applicationVersionEntity.setCreatedTime(LocalDateTime.now());
        applicationVersionEntity.setUpdatedTime(LocalDateTime.now());

        DbChain.table(ApplicationVersionEntity.class).save(applicationVersionEntity);

        for(String downloadUrl:createApplicationVersionRequest.getDownloadUrlList()){
            ApplicationVersionDownloadEntity applicationVersionDownloadEntity = new ApplicationVersionDownloadEntity();
            applicationVersionDownloadEntity.setId(UlidCreator.getUlid().toString());
            applicationVersionDownloadEntity.setApplicationVersionId(applicationVersionEntity.getId());
            applicationVersionDownloadEntity.setUrl(downloadUrl);
            applicationVersionDownloadEntity.setCreatedTime(LocalDateTime.now());

            DbChain.table(ApplicationVersionDownloadEntity.class).save(applicationVersionDownloadEntity);
        }

        return Result.success();
    }

    public Result updateApplicationVersion(@Valid UpdateApplicationVersionRequest updateApplicationVersionRequest) {
        ApplicationVersionEntity applicationVersionEntity = QueryChain.of(ApplicationVersionEntity.class ).select()
                .from(ApplicationVersionEntity.class)
                .where(ApplicationVersion.Id.eq(updateApplicationVersionRequest.getApplicationVersionId()))
                .one();
        if(applicationVersionEntity ==null){
            return Result.businessError("应用版本不存在");
        }

        UpdateChain.of(ApplicationVersionEntity.class)
                .set(ApplicationVersion.Version, updateApplicationVersionRequest.getVersion())
                .set(ApplicationVersion.Notes, updateApplicationVersionRequest.getNotes())
                .set(ApplicationVersion.ReleaseTime, updateApplicationVersionRequest.getReleaseTime())
                .set(ApplicationVersion.UpdatedTime, LocalDateTime.now())
                .where(ApplicationVersion.Id.eq(updateApplicationVersionRequest.getApplicationVersionId()))
                .update();

        DbChain.table(ApplicationVersionDownloadEntity.class)
                .where(ApplicationVersionDownload.ApplicationVersionId.eq(updateApplicationVersionRequest.getApplicationVersionId()))
                .remove();

        for(String downloadUrl:updateApplicationVersionRequest.getDownloadUrlList()){
            ApplicationVersionDownloadEntity applicationVersionDownloadEntity = new ApplicationVersionDownloadEntity();
            applicationVersionDownloadEntity.setId(UlidCreator.getUlid().toString());
            applicationVersionDownloadEntity.setApplicationVersionId(updateApplicationVersionRequest.getApplicationVersionId());
            applicationVersionDownloadEntity.setUrl(downloadUrl);
            applicationVersionDownloadEntity.setCreatedTime(LocalDateTime.now());

            DbChain.table(ApplicationVersionDownloadEntity.class).save(applicationVersionDownloadEntity);
        }

        return Result.success();
    }

    public Result publishApplicationVersion(@NotBlank(message = "应用版本id不能为空") String applicationVersionId) {
        ApplicationVersionEntity applicationVersionEntity = QueryChain.of(ApplicationVersionEntity.class ).select()
                .from(ApplicationVersionEntity.class)
                .where(ApplicationVersion.Id.eq(applicationVersionId))
                .one();
        if(applicationVersionEntity ==null){
            return Result.businessError("应用版本不存在");
        }
        if(ApplicationVersionStatusEnum.ENABLE.getCode().equals(applicationVersionEntity.getStatus())){
            return Result.success("应用版本已发布");
        }

        UpdateChain.of(ApplicationVersionEntity.class)
                .set(ApplicationVersion.Status, ApplicationVersionStatusEnum.ENABLE.getCode())
                .set(ApplicationVersion.UpdatedTime, LocalDateTime.now())
                .where(ApplicationVersion.Id.eq(applicationVersionId))
                .update();
        return Result.success();
    }

    public Result disableApplicationVersion(@NotBlank(message = "应用版本id不能为空") String applicationVersionId) {
        ApplicationVersionEntity applicationVersionEntity = QueryChain.of(ApplicationVersionEntity.class ).select()
                .from(ApplicationVersionEntity.class)
                .where(ApplicationVersion.Id.eq(applicationVersionId))
                .one();
        if(applicationVersionEntity ==null){
            return Result.businessError("应用版本不存在");
        }
        if(ApplicationVersionStatusEnum.DISABLE.getCode().equals(applicationVersionEntity.getStatus())){
            return Result.success("应用版本已停用");
        }

        UpdateChain.of(ApplicationVersionEntity.class)
                .set(ApplicationVersion.Status, ApplicationVersionStatusEnum.DISABLE.getCode())
                .set(ApplicationVersion.UpdatedTime, LocalDateTime.now())
                .where(ApplicationVersion.Id.eq(applicationVersionId))
                .update();
        return Result.success();
    }
}