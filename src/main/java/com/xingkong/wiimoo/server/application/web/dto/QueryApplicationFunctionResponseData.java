package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel(description = "应用功能定义信息")
public class QueryApplicationFunctionResponseData {
    @ApiModelProperty(value = "功能id", required = true)
    private String id;
    @ApiModelProperty(value = "应用编码", required = true)
    private String applicationCode;
    @ApiModelProperty(value = "功能编码", required = true)
    private String code;
    @ApiModelProperty(value = "功能名称", required = true)
    private String name;
    @ApiModelProperty(value = "功能说明", required = true)
    private String description;
    @ApiModelProperty(value = "每次使用的积分", required = true)
    private Integer eachUseScore;
    @ApiModelProperty(value = "功能状态", required = true)
    private String status;
    @ApiModelProperty(value = "功能生效时间", required = true)
    private LocalDateTime startTime;
    @ApiModelProperty(value = "功能失效时间", required = true)
    private LocalDateTime endTime;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getEachUseScore() {
        return eachUseScore;
    }

    public void setEachUseScore(Integer eachUseScore) {
        this.eachUseScore = eachUseScore;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
}