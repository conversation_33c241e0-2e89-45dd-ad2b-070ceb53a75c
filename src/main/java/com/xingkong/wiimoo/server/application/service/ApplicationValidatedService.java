package com.xingkong.wiimoo.server.application.service;

import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.common.support.ApplicationStatusEnum;
import com.xingkong.wiimoo.server.application.db.entity.ApplicationEntity;
import org.springframework.stereotype.Service;

import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationTableDef.Application;

@Service
public class ApplicationValidatedService {

    public Result<ApplicationEntity> isExist(String applicationCode) {
        ApplicationEntity applicationEntity = QueryChain.of(ApplicationEntity.class).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(applicationCode))
                .one();
        if (applicationEntity == null) {
            return Result.businessError("应用不存在");
        }
        return Result.success(applicationEntity);
    }

    public Result<ApplicationEntity> isValid(String applicationCode) {
        ApplicationEntity applicationEntity = QueryChain.of(ApplicationEntity.class).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(applicationCode))
                .one();
        Result result = isValid(applicationEntity);
        if(!ResultCode.SUCCESS.getCode().equals(result.getResultCode())){
            return result;
        }
        return Result.success(applicationEntity);
    }

    public Result isValid(ApplicationEntity applicationEntity) {
        if (applicationEntity == null) {
            return Result.businessError("应用不存在");
        }
        if (!ApplicationStatusEnum.ENABLE.getCode().equals(applicationEntity.getStatus())) {
            if(ApplicationStatusEnum.WAITING.getCode().equals(applicationEntity.getStatus())){
                return Result.businessError("应用未发布");
            }
            return Result.businessError("应用已关停");
        }
        return Result.success();
    }


}