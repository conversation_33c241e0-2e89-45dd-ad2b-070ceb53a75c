package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "添加VIP权限请求对象")
public class AddVipPermissionRequest {
    @ApiModelProperty(value = "VIPID", required = true)
    @NotBlank(message = "VIPID不能为空")
    private String vipId;
    @ApiModelProperty(value = "资源ID", required = true)
    @NotBlank(message = "资源ID不能为空")
    private String resourceId;

    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }
}