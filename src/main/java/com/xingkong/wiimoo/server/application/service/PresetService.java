package com.xingkong.wiimoo.server.application.service;

import cn.dev33.satoken.stp.StpLogic;
import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.application.db.entity.PresetEntity;
import com.xingkong.wiimoo.server.application.web.dto.CreatePresetRequest;
import com.xingkong.wiimoo.server.application.web.dto.CreatePresetResopnseData;
import com.xingkong.wiimoo.server.application.web.dto.QueryPresetResponseData;
import com.xingkong.wiimoo.server.application.web.dto.UpdatePresetRequest;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

import static com.xingkong.wiimoo.server.application.db.entity.table.PresetTableDef.Preset;

@Service
@Validated
public class PresetService {
    public Result<CreatePresetResopnseData> createPreset(@NotBlank(message = "应用编码不能为空") String applicationCode, @Valid CreatePresetRequest createPresetRequest) {

        PresetEntity presetEntity = new PresetEntity();
        presetEntity.setId(UlidCreator.getUlid().toString());
        presetEntity.setApplicationCode(applicationCode);
        presetEntity.setConsumerId(StpKit.getStpLogic(applicationCode).getLoginIdAsString());
        presetEntity.setName(createPresetRequest.getName());
        presetEntity.setContent(createPresetRequest.getContent());
        presetEntity.setCreatedTime(LocalDateTime.now());
        presetEntity.setUpdatedTime(LocalDateTime.now());

        DbChain.table(PresetEntity.class).save(presetEntity);

        CreatePresetResopnseData createPresetResopnseData = new CreatePresetResopnseData();
        createPresetResopnseData.setPresetId(presetEntity.getId());
        return Result.success(createPresetResopnseData);

    }

    public Result updatePreset(@Valid UpdatePresetRequest updatePresetRequest) {
        PresetEntity presetEntity = QueryChain.of(PresetEntity.class).select()
                .from(PresetEntity.class)
                .where(Preset.Id.eq(updatePresetRequest.getId()))
                .one();
        if (presetEntity == null) {
            return Result.businessError("预设不存在");
        }
        StpLogic stpLogic = StpKit.getStpLogic(presetEntity.getApplicationCode());
        stpLogic.checkLogin();
        if (!presetEntity.getConsumerId().equals(stpLogic.getLoginIdAsString())) {
            return Result.businessError("预设不存在");
        }
        UpdateChain.of(PresetEntity.class)
                .set(Preset.Name, updatePresetRequest.getName())
                .set(Preset.Content, updatePresetRequest.getContent())
                .set(Preset.UpdatedTime, LocalDateTime.now())
                .where(Preset.Id.eq(updatePresetRequest.getId()))
                .update();

        return Result.success();
    }

    public Result deletePreset(@NotBlank(message = "预设id不能为空") String id) {
        PresetEntity presetEntity = QueryChain.of(PresetEntity.class).select()
                .from(PresetEntity.class)
                .where(Preset.Id.eq(id))
                .one();
        if (presetEntity == null) {
            return Result.success("预设不存在或已删除");
        }
        StpLogic stpLogic = StpKit.getStpLogic(presetEntity.getApplicationCode());
        stpLogic.checkLogin();
        if (!presetEntity.getConsumerId().equals(stpLogic.getLoginIdAsString())) {
            return Result.businessError("预设不存在");
        }
        DbChain.table(PresetEntity.class)
                .where(Preset.Id.eq(id))
                .remove();

        return Result.success();
    }

    public Result<List<QueryPresetResponseData>> queryPreset(@NotBlank(message = "应用编码不能为空") String applicationCode) {
        StpLogic stpLogic = StpKit.getStpLogic(applicationCode);
        stpLogic.checkLogin();
        List<QueryPresetResponseData> queryPresetResponseDataList = QueryChain.of(PresetEntity.class).select()
                .from(PresetEntity.class)
                .where(Preset.ConsumerId.eq(stpLogic.getLoginIdAsString()))
                .orderBy(Preset.UpdatedTime, false)
                .listAs(QueryPresetResponseData.class);
        return Result.success(queryPresetResponseDataList);
    }
}