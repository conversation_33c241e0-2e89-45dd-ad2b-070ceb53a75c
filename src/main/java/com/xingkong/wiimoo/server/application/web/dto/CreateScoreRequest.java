package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "创建积分请求对象")
public class CreateScoreRequest {
   @ApiModelProperty(value = "积分单位数量", required = true)
   @NotNull(message = "积分单位数量不能为空")
   private Integer unitQuantity;
   @ApiModelProperty(value = "积分价格", required = true)
   @NotNull(message = "积分价格不能为空")
   @Min(value = 0, message = "积分价格不能小于0")
   private BigDecimal price;
   @ApiModelProperty(value = "生效时间", required = true)
   @NotNull(message = "生效时间不能为空")
   private LocalDateTime startTime;
   @ApiModelProperty(value = "失效时间", required = true)
   @NotNull(message = "失效时间不能为空")
   private LocalDateTime endTime;

    public Integer getUnitQuantity() {
        return unitQuantity;
    }

    public void setUnitQuantity(Integer unitQuantity) {
        this.unitQuantity = unitQuantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}