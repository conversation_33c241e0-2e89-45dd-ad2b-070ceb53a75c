package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel(description = "查询VIP信息响应对象")
public class QueryVipResponseData {
    @ApiModelProperty(value = "VIP id", required = true)
    private String vipId;
    @ApiModelProperty(value = "应用编码", required = true)
    private String applicationCode;
    @ApiModelProperty(value = "应用名称", required = true)
    private String applicationName;
    @ApiModelProperty(value = "应用状态", required = true)
    private String applicationStatus;
    @ApiModelProperty(value = "VIP编码", required = true)
    private String vipCode;
    @ApiModelProperty(value = "VIP名称", required = true)
    private String vipName;
    @ApiModelProperty(value = "VIP说明", required = true)
    private String description;
    @ApiModelProperty(value = "VIP状态", required = true)
    private String vipStatus;
    @ApiModelProperty(value = "VIP生效时间", required = true)
    private LocalDateTime startTime;
    @ApiModelProperty(value = "VIP失效时间", required = true)
    private LocalDateTime endTime;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;
    @ApiModelProperty(value = "更新时间", required = true)
    private LocalDateTime updatedTime;

    public String getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(String applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getVipCode() {
        return vipCode;
    }

    public void setVipCode(String vipCode) {
        this.vipCode = vipCode;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVipStatus() {
        return vipStatus;
    }

    public void setVipStatus(String vipStatus) {
        this.vipStatus = vipStatus;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}