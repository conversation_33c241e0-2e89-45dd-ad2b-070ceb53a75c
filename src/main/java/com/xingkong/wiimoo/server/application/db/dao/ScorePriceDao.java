package com.xingkong.wiimoo.server.application.db.dao;

import com.mybatisflex.core.BaseMapper;
import com.xingkong.wiimoo.server.application.db.entity.ScorePriceEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ScorePriceDao extends BaseMapper<ScorePriceEntity> {
//    @Select("select * from score_price ${ew.customSqlSegment}")
//    List<ScorePriceInfo> selectListByMapper(@Param(Constants.WRAPPER) LambdaQueryWrapper<ScorePriceEntity> queryWrapper);
}