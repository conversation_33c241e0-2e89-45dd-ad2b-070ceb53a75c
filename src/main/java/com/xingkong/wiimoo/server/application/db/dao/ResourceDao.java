package com.xingkong.wiimoo.server.application.db.dao;

import com.mybatisflex.core.BaseMapper;
import com.xingkong.wiimoo.server.application.db.entity.ResourceEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ResourceDao extends BaseMapper<ResourceEntity> {

//    @Select("select * from resource ${ew.customSqlSegment}")
//    List<GetApplicationFunctionResponseData> selectApplicationFunctions(@Param(Constants.WRAPPER) LambdaQueryWrapper<ResourceEntity> queryWrapper);
}