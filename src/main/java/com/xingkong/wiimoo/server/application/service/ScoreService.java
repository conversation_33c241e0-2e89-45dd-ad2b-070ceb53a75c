package com.xingkong.wiimoo.server.application.service;

import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.support.ScorePriceStatusEnum;
import com.xingkong.wiimoo.server.application.db.entity.ScorePriceEntity;
import com.xingkong.wiimoo.server.application.web.dto.CreateScoreRequest;
import com.xingkong.wiimoo.server.application.web.dto.QueryScorePriceResponseData;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

import static com.xingkong.wiimoo.server.application.db.entity.table.ScorePriceTableDef.ScorePrice;

@Service
public class ScoreService {

    public Result<List<QueryScorePriceResponseData>> queryScorePriceInfo() {
        List<QueryScorePriceResponseData> queryScorePriceResponseDataList =  QueryChain.of(ScorePriceEntity.class).select()
                .from(ScorePriceEntity.class)
                .where(ScorePrice.StartTime.le(LocalDateTime.now()))
                .and(ScorePrice.EndTime.ge(LocalDateTime.now()))
                .orderBy(ScorePrice.CreatedTime, false)
                .listAs(QueryScorePriceResponseData.class);
        return Result.success(queryScorePriceResponseDataList);
    }

    public Result createScore(@Valid CreateScoreRequest createScoreRequest) {
        ScorePriceEntity scorePriceEntity = new ScorePriceEntity();
        scorePriceEntity.setId(UlidCreator.getUlid().toString());
        scorePriceEntity.setUnitQuantity(createScoreRequest.getUnitQuantity());
        scorePriceEntity.setPrice(createScoreRequest.getPrice());
        scorePriceEntity.setStatus(ScorePriceStatusEnum.WAITING.getCode());
        scorePriceEntity.setStartTime(createScoreRequest.getStartTime());
        scorePriceEntity.setEndTime(createScoreRequest.getEndTime());
        scorePriceEntity.setCreatedTime(LocalDateTime.now());

        DbChain.table(ScorePriceEntity.class).save(scorePriceEntity);

        return Result.success();
    }

    public Result enableScore(@NotBlank(message = "积分价格id不能为空") String scorePriceId) {
        ScorePriceEntity scorePriceEntity =  QueryChain.of(ScorePriceEntity.class).select()
                .from(ScorePriceEntity.class)
                .where(ScorePrice.Id.eq(scorePriceId))
                .one();
        if (scorePriceEntity == null) {
            return Result.businessError("积分价格不存在");
        }

        UpdateChain.of(ScorePriceEntity.class)
                .set(ScorePrice.Status, ScorePriceStatusEnum.ENABLE.getCode())
                .set(ScorePrice.UpdatedTime, LocalDateTime.now())
                .where(ScorePrice.Id.eq(scorePriceId))
                .update();

        return Result.success();
    }

    public Result disableScore(@NotBlank(message = "积分价格id不能为空") String scorePriceId) {
        ScorePriceEntity scorePriceEntity =  QueryChain.of(ScorePriceEntity.class).select()
                .from(ScorePriceEntity.class)
                .where(ScorePrice.Id.eq(scorePriceId))
                .one();
        if (scorePriceEntity == null) {
            return Result.businessError("积分价格不存在");
        }

        UpdateChain.of(ScorePriceEntity.class)
                .set(ScorePrice.Status, ScorePriceStatusEnum.DISABLE.getCode())
                .set(ScorePrice.UpdatedTime, LocalDateTime.now())
                .where(ScorePrice.Id.eq(scorePriceId))
                .update();
        return Result.success();
    }
}