package com.xingkong.wiimoo.server.application.service;

import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.common.support.ResourceStatusEnum;
import com.xingkong.wiimoo.server.application.db.entity.ResourceEntity;
import com.xingkong.wiimoo.server.application.web.dto.CreateResourceRequest;
import com.xingkong.wiimoo.server.application.web.dto.UpdateResourceRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

import static com.xingkong.wiimoo.server.application.db.entity.table.ResourceTableDef.Resource;

@Service
@Validated
public class ResourceService {

    @Autowired
    private ApplicationValidatedService applicationValidatedService;

    public Result createResource(@Valid CreateResourceRequest createResourceRequest) {

        Result result = applicationValidatedService.isExist(createResourceRequest.getApplicationCode());
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }

        ResourceEntity resourceEntity = new ResourceEntity();
        resourceEntity.setId(UlidCreator.getUlid().toString());
        resourceEntity.setApplicationCode(createResourceRequest.getApplicationCode());
        resourceEntity.setCode(createResourceRequest.getCode());
        resourceEntity.setName(createResourceRequest.getName());
        resourceEntity.setDescription(createResourceRequest.getDescription());
        resourceEntity.setType(createResourceRequest.getType());
        resourceEntity.setEachUseScore(createResourceRequest.getEachUseScore());
        resourceEntity.setStatus(ResourceStatusEnum.WAITING.getCode());
        resourceEntity.setStartTime(createResourceRequest.getStartTime());
        resourceEntity.setEndTime(createResourceRequest.getEndTime());
        resourceEntity.setCreatedTime(LocalDateTime.now());

        DbChain.table(ResourceEntity.class).save(resourceEntity);

        return Result.success();
    }

    public Result updateResource(@Valid UpdateResourceRequest updateResourceRequest) {
        ResourceEntity resourceEntity = QueryChain.of(ResourceEntity.class).select()
                .from(ResourceEntity.class)
                .where(Resource.Id.eq(updateResourceRequest.getId()))
                .one();
        if (resourceEntity == null) {
            return Result.businessError("资源不存在");
        }

        UpdateChain.of(ResourceEntity.class)
                .set(Resource.Name, updateResourceRequest.getName())
                .set(Resource.Description, updateResourceRequest.getDescription())
                .set(Resource.EachUseScore, updateResourceRequest.getEachUseScore())
                .set(Resource.StartTime, updateResourceRequest.getStartTime())
                .set(Resource.EndTime, updateResourceRequest.getEndTime())
                .set(Resource.UpdatedTime, LocalDateTime.now())
                .where(Resource.Id.eq(updateResourceRequest.getId()))
                .update();

        return Result.success();
    }

    public Result enableResource(@NotBlank(message = "资源id不能为空") String resourceId) {
        ResourceEntity resourceEntity = QueryChain.of(ResourceEntity.class).select()
                .from(ResourceEntity.class)
                .where(Resource.Id.eq(resourceId))
                .one();
        if (resourceEntity == null) {
            return Result.businessError("资源不存在");
        }
        if (ResourceStatusEnum.ENABLE.getCode().equals(resourceEntity.getStatus())) {
            return Result.success("资源已启用");
        }

        UpdateChain.of(ResourceEntity.class)
                .set(Resource.Status, ResourceStatusEnum.ENABLE.getCode())
                .set(Resource.UpdatedTime, LocalDateTime.now())
                .where(Resource.Id.eq(resourceId))
                .update();

        return Result.success();
    }

    public Result disableResource(@NotBlank(message = "资源id不能为空") String resourceId) {
        ResourceEntity resourceEntity = QueryChain.of(ResourceEntity.class).select()
                .from(ResourceEntity.class)
                .where(Resource.Id.eq(resourceId))
                .one();
        if (resourceEntity == null) {
            return Result.businessError("资源不存在");
        }
        if (ResourceStatusEnum.DISABLE.getCode().equals(resourceEntity.getStatus())) {
            return Result.success("资源已停用");
        }

        UpdateChain.of(ResourceEntity.class)
                .set(Resource.Status, ResourceStatusEnum.DISABLE.getCode())
                .set(Resource.UpdatedTime, LocalDateTime.now())
                .where(Resource.Id.eq(resourceId))
                .update();

        return Result.success();
    }
}