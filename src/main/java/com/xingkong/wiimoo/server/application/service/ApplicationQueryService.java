package com.xingkong.wiimoo.server.application.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.server.application.db.entity.*;
import com.xingkong.wiimoo.server.application.support.ResourceType;
import com.xingkong.wiimoo.server.application.web.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationTableDef.Application;
import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationVersionDownloadTableDef.ApplicationVersionDownload;
import static com.xingkong.wiimoo.server.application.db.entity.table.ApplicationVersionTableDef.ApplicationVersion;
import static com.xingkong.wiimoo.server.application.db.entity.table.ResourceTableDef.Resource;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipPermissionTableDef.VipPermission;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipPriceTableDef.VipPrice;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipTableDef.Vip;


@Service
@Validated
public class ApplicationQueryService {

    private static final Logger log = LoggerFactory.getLogger(ApplicationQueryService.class);
    @Autowired
    private ApplicationValidatedService applicationValidatedService;

    public Result<Page<QueryApplicationResponseData>> queryApplicationPage(@Valid QueryApplicationPageRequest queryApplicationPageRequest) {

        Page<QueryApplicationResponseData> queryApplicationResponseDataList = QueryChain.of(ApplicationEntity.class).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(queryApplicationPageRequest.getCode()).when(!StringUtils.isEmpty(queryApplicationPageRequest.getCode())))
                .and(Application.Name.eq(queryApplicationPageRequest.getName()).when(!StringUtils.isEmpty(queryApplicationPageRequest.getName())))
                .and(Application.Status.in(queryApplicationPageRequest.getStatusList()).when(!CollectionUtils.isEmpty(queryApplicationPageRequest.getStatusList())))
                .pageAs(new Page<>(queryApplicationPageRequest.getPage().getPageNumber(), queryApplicationPageRequest.getPage().getPageSize()), QueryApplicationResponseData.class);
        return Result.success(queryApplicationResponseDataList);
    }


    public Result<QueryApplicationResponseData> queryApplication(@Valid @NotBlank(message = "应用编码不能为空") String applicationCode) {
        QueryApplicationResponseData queryApplicationResponseData = QueryChain.of(ApplicationEntity.class).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(applicationCode))
                .oneAs(QueryApplicationResponseData.class);
        if (queryApplicationResponseData == null) {
            return Result.businessError("应用不存在");
        }
        return Result.success(queryApplicationResponseData);
    }

    public Result<List<QueryApplicationFunctionResponseData>> queryApplicationFunctions(@NotBlank(message = "应用编码不能为空") String applicationCode) {
        Result result = applicationValidatedService.isValid(applicationCode);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }
        List<QueryApplicationFunctionResponseData> queryApplicationFunctionResponseDataList = QueryChain.of(ResourceEntity.class).select()
                .from(ResourceEntity.class)
                .where(Resource.ApplicationCode.eq(applicationCode))
                .and(Resource.Type.eq(ResourceType.FUNCTION.getCode()))
                .orderBy(Resource.CreatedTime, false)
                .listAs(QueryApplicationFunctionResponseData.class);

        return Result.success(queryApplicationFunctionResponseDataList);
    }


    @Value("${storeBaseUrl}")
    private String storeBaseUrl;

    public Result<CheckVersionResponseData> checkVersion(@NotBlank(message = "应用编码不能为空") String applicationCode,
                                                         @NotBlank(message = "版本号不能为空") String version) {

        Result result = applicationValidatedService.isValid(applicationCode);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }

        ApplicationVersionEntity newVersionEntity = QueryChain.of(ApplicationVersionEntity.class).select()
                .from(ApplicationVersionEntity.class)
                .where(ApplicationVersion.ApplicationCode.eq(applicationCode))
                .and(ApplicationVersion.Version.eq(version))
                .one();

        CheckVersionResponseData checkVersionResponseData = new CheckVersionResponseData();

        if (version.compareToIgnoreCase(newVersionEntity.getVersion()) < 0) {
            checkVersionResponseData.setHasNewVersion(true);
        } else {
            checkVersionResponseData.setHasNewVersion(false);
        }
        checkVersionResponseData.setNewVersion(newVersionEntity.getVersion());
        checkVersionResponseData.setDownloadPageUrl(storeBaseUrl + "/applications/" + applicationCode + "?tab=releaseNotes");


        return Result.success(checkVersionResponseData);
    }

    public Result<QueryVersionResponseData> queryLatestVersion(@NotBlank(message = "应用编码不能为空") String applicationCode) {
        Result result = applicationValidatedService.isValid(applicationCode);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }
        QueryVersionResponseData queryVersionResponseData = QueryChain.of(ApplicationVersionEntity.class).select()
                .from(ApplicationVersionEntity.class)
                .where(ApplicationVersion.ApplicationCode.eq(applicationCode))
                .and(ApplicationVersion.ReleaseTime.le(LocalDateTime.now()))
                .orderBy(ApplicationVersion.ReleaseTime, false)
                .oneAs(QueryVersionResponseData.class);
        if (queryVersionResponseData == null) {
            return Result.businessError("应用没有版本");
        }
        List<String> downloadUrlList = QueryChain.of(ApplicationVersionDownloadEntity.class).select(ApplicationVersionDownload.Url)
                .from(ApplicationVersionDownloadEntity.class)
                .where(ApplicationVersionDownload.ApplicationVersionId.eq(queryVersionResponseData.getApplicationVersionId()))
                .listAs(String.class);

        queryVersionResponseData.setDownloadUrlList(downloadUrlList);

        return Result.success(queryVersionResponseData);
    }


    public Result<Page<QueryApplicationVersionResponseData>> queryApplicationVersionPage(@Valid QueryApplicationVersionPageRequest queryApplicationPageRequest) {
        Page<QueryApplicationVersionResponseData> page = QueryChain.of(ApplicationVersionEntity.class).select(ApplicationVersion.Id.as(QueryApplicationVersionResponseData::getApplicationVersionId),
                        ApplicationVersion.ApplicationCode,
                        Application.Name.as(QueryApplicationVersionResponseData::getApplicationName),
                        Application.Status.as(QueryApplicationVersionResponseData::getApplicationStatus),
                        Application.StartTime.as(QueryApplicationVersionResponseData::getApplicationStartTime),
                        Application.EndTime.as(QueryApplicationVersionResponseData::getApplicationEndTime),
                        ApplicationVersion.Version,
                        ApplicationVersion.Notes,
                        ApplicationVersion.Status.as(QueryApplicationVersionResponseData::getVersionStatus),
                        ApplicationVersion.ReleaseTime,
                        ApplicationVersion.CreatedTime,
                        ApplicationVersion.UpdatedTime).from(ApplicationVersionEntity.class)
                .leftJoin(ApplicationEntity.class).on(ApplicationVersionEntity::getApplicationCode, ApplicationEntity::getCode)
                .where(ApplicationVersionEntity::getApplicationCode).eq(queryApplicationPageRequest.getApplicationCode(), !StringUtils.isEmpty(queryApplicationPageRequest.getApplicationCode()))
                .and(ApplicationEntity::getStatus).in(queryApplicationPageRequest.getApplicationStatusList(), !CollectionUtils.isEmpty(queryApplicationPageRequest.getApplicationStatusList()))
                .and(ApplicationVersionEntity::getVersion).eq(queryApplicationPageRequest.getVersion(), !StringUtils.isEmpty(queryApplicationPageRequest.getVersion()))
                .and(ApplicationVersionEntity::getStatus).in(queryApplicationPageRequest.getVersionStatusList(), !CollectionUtils.isEmpty(queryApplicationPageRequest.getVersionStatusList()))
                .orderBy(ApplicationVersionEntity::getReleaseTime, false)
                .pageAs(new Page<>(queryApplicationPageRequest.getPage().getPageNumber(), queryApplicationPageRequest.getPage().getPageSize()), QueryApplicationVersionResponseData.class);

        for (QueryApplicationVersionResponseData queryApplicationVersionResponseData : page.getRecords()) {
            queryApplicationVersionResponseData.setDownloadUrlList(QueryChain.of(ApplicationVersionDownloadEntity.class).select(
                            ApplicationVersionDownload.Url
                    )
                    .from(ApplicationVersionDownloadEntity.class)
                    .where(ApplicationVersionDownload.ApplicationVersionId.eq(queryApplicationVersionResponseData.getApplicationVersionId()))
                    .listAs(String.class));
        }

        return Result.success(page);
    }

    public Result<QueryApplicationAllInfoResponseData> queryApplicationAllInfo(@NotBlank(message = "应用编码不能为空") String applicationCode) {
        Result<ApplicationEntity> result = applicationValidatedService.isValid(applicationCode);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return Result.simpleFail(result);
        }
        QueryApplicationAllInfoResponseData queryApplicationAllInfoResponseData = new QueryApplicationAllInfoResponseData();

        QueryApplicationAllInfoResponseData.ApplicationInfo applicationInfo = QueryChain.of(ApplicationEntity.class).select()
                .from(ApplicationEntity.class)
                .where(Application.Code.eq(applicationCode))
                .oneAs(QueryApplicationAllInfoResponseData.ApplicationInfo.class);

        queryApplicationAllInfoResponseData.setApplicationInfo(applicationInfo);


        queryApplicationAllInfoResponseData.setVipInfoList(
                QueryChain.of(VipEntity.class).select()
                        .from(VipEntity.class)
                        .where(Vip.ApplicationCode.eq(applicationCode))
                        .listAs(QueryApplicationAllInfoResponseData.VipInfo.class)
        );

        for (QueryApplicationAllInfoResponseData.VipInfo vipInfo : queryApplicationAllInfoResponseData.getVipInfoList()) {
            vipInfo.setPriceList(QueryChain.of(VipPriceEntity.class).select(
                                    VipPrice.Id.as(QueryApplicationAllInfoResponseData.VipInfo.VipPriceInfo::getVipPriceId),
                                    VipPrice.Price.as(QueryApplicationAllInfoResponseData.VipInfo.VipPriceInfo::getPrice),
                                    VipPrice.Duration.as(QueryApplicationAllInfoResponseData.VipInfo.VipPriceInfo::getDuration),
                                    VipPrice.DurationUnit.as(QueryApplicationAllInfoResponseData.VipInfo.VipPriceInfo::getDurationUnit),
                                    VipPrice.Score.as(QueryApplicationAllInfoResponseData.VipInfo.VipPriceInfo::getScore),
                                    VipPrice.BuyTimes.as(QueryApplicationAllInfoResponseData.VipInfo.VipPriceInfo::getBuyTimes)
                            )
                            .from(VipPriceEntity.class)
                            .where(VipPrice.VipId.eq(vipInfo.getId()))
                            .listAs(QueryApplicationAllInfoResponseData.VipInfo.VipPriceInfo.class)
            );
            vipInfo.setVipPermissionInfoList(QueryChain.of(VipPermissionEntity.class).select(
                                    VipPermission.Id.as(QueryApplicationAllInfoResponseData.VipInfo.VipPermissionInfo::getPermissionId),
                                    VipPermission.ResourceId.as(QueryApplicationAllInfoResponseData.VipInfo.VipPermissionInfo::getResourceId),
                                    Resource.Name.as(QueryApplicationAllInfoResponseData.VipInfo.VipPermissionInfo::getResourceName),
                                    Resource.EachUseScore.as(QueryApplicationAllInfoResponseData.VipInfo.VipPermissionInfo::getEachUseScore)
                            )
                            .from(VipPermissionEntity.class)
                            .leftJoin(ResourceEntity.class).on(VipPermissionEntity::getResourceId, ResourceEntity::getId)
                            .where(VipPermission.VipId.eq(vipInfo.getId()))
                            .listAs(QueryApplicationAllInfoResponseData.VipInfo.VipPermissionInfo.class)
            );
        }
        return Result.success(queryApplicationAllInfoResponseData);
    }


}