package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "创建应用版本请求对象")
public class CreateApplicationVersionRequest {
    @ApiModelProperty(value = "应用编码", required = true)
    @NotBlank(message = "应用编码不能为空")
    private String applicationCode;
    @ApiModelProperty(value = "版本号", required = true)    
    @NotBlank(message = "版本号不能为空")
    private String version;
    @ApiModelProperty(value = "版本描述", required = true)
    @NotBlank(message = "版本描述不能为空")
    private String notes;
    @ApiModelProperty(value = "下载地址", required = true)
    @NotNull(message = "下载地址不能为空")
    @NotEmpty(message = "下载地址不能为空")
    private List<String> downloadUrlList;
    @ApiModelProperty(value = "发布时间", required = true)
    @NotNull(message = "发布时间不能为空")
    private LocalDateTime releaseTime;

    public List<String> getDownloadUrlList() {
        return downloadUrlList;
    }

    public void setDownloadUrlList(List<String> downloadUrlList) {
        this.downloadUrlList = downloadUrlList;
    }

    public LocalDateTime getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(LocalDateTime releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    
}