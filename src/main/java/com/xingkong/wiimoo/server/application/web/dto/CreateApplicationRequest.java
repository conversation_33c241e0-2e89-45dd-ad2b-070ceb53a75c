package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel(description = "创建应用请求对象")
public class CreateApplicationRequest {
    @ApiModelProperty(value = "应用编码", required = true)
    @NotBlank(message = "应用编码不能为空")
    private String code;
    @ApiModelProperty(value = "应用名称", required = true)
    @NotBlank(message = "应用名称不能为空")
    private String name;
    @ApiModelProperty(value = "应用说明", required = true)
    @NotBlank(message = "应用说明不能为空")
    private String description;
    @ApiModelProperty(value = "应用主图地址", required = true)
    @NotBlank(message = "应用主图地址不能为空")
    private String mainImageUrl;
    @ApiModelProperty(value = "使用说明文档地址", required = false)
    private String useDocUrl;
    @ApiModelProperty(value = "应用生效时间", required = true)
    @NotNull(message = "应用生效时间不能为空")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "应用失效时间", required = true)
    @NotNull(message = "应用失效时间不能为空")
    private LocalDateTime endTime;

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMainImageUrl() {
        return mainImageUrl;
    }

    public void setMainImageUrl(String mainImageUrl) {
        this.mainImageUrl = mainImageUrl;
    }

    public String getUseDocUrl() {
        return useDocUrl;
    }

    public void setUseDocUrl(String useDocUrl) {
        this.useDocUrl = useDocUrl;
    }
}