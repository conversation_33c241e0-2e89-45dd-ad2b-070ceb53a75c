package com.xingkong.wiimoo.server.application.web.controller;

import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.application.service.ScoreService;
import com.xingkong.wiimoo.server.application.web.dto.CreateScoreRequest;
import com.xingkong.wiimoo.server.application.web.dto.QueryScorePriceResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "积分服务接口")
@RestController
@RequestMapping("/score")
public class ScoreController {
    
    @Autowired
    private ScoreService scoreService;

    @ApiOperation(value = "创建积分价格接口",notes = "创建积分价格")
    @PostMapping("/create")
    @ResponseBody
    public Result createScore(@RequestBody CreateScoreRequest createScoreRequest) {
        return scoreService.createScore(createScoreRequest);
    }

    @ApiOperation(value = "启用积分价格接口",notes = "启用积分价格")
    @PostMapping("/enable")
    @ResponseBody
    public Result enableScore(@RequestParam(value = "scorePriceId", required = true) String scorePriceId) {
        return scoreService.enableScore(scorePriceId);
    }

    @ApiOperation(value = "停用积分价格接口",notes = "停用积分价格")
    @PostMapping("/disable")
    @ResponseBody
    public Result disableScore(@RequestParam(value = "scorePriceId", required = true) String scorePriceId) {
        return scoreService.disableScore(scorePriceId);
    }

    @ApiOperation(value = "获取积分定价信息接口",notes = "获取积分定价信息接口")
    @GetMapping("/price/query")
    @ResponseBody
    public Result<List<QueryScorePriceResponseData>> queryScorePriceInfo() {
        return scoreService.queryScorePriceInfo();
    }
    
}