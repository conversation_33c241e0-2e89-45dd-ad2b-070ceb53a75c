package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "应用历史版本信息")
public class QueryApplicationVersionResponseData {
    @ApiModelProperty(value = "版本id", required = true)
    private String applicationVersionId;
    @ApiModelProperty(value = "应用编码", required = true)
    private String applicationCode;
    @ApiModelProperty(value = "应用名称", required = true)
    private String applicationName;
    @ApiModelProperty(value = "应用状态", required = true)
    private String applicationStatus;
    @ApiModelProperty(value = "应用生效时间", required = true)
    private LocalDateTime applicationStartTime;
    @ApiModelProperty(value = "应用失效时间", required = true)
    private LocalDateTime applicationEndTime;
    @ApiModelProperty(value = "版本号", required = true)
    private String version;
    @ApiModelProperty(value = "更新说明", required = true)
    private String notes;
    @ApiModelProperty(value = "状态", required = true)
    private String versionStatus;
    @ApiModelProperty(value = "下载地址", required = true)
    private List<String> downloadUrlList;
    @ApiModelProperty(value = "发布时间", required = true)
    private LocalDateTime releaseTime;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;
    @ApiModelProperty(value = "更新时间", required = true)
    private LocalDateTime updatedTime;

    public String getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(String applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public LocalDateTime getApplicationStartTime() {
        return applicationStartTime;
    }

    public void setApplicationStartTime(LocalDateTime applicationStartTime) {
        this.applicationStartTime = applicationStartTime;
    }

    public LocalDateTime getApplicationEndTime() {
        return applicationEndTime;
    }

    public void setApplicationEndTime(LocalDateTime applicationEndTime) {
        this.applicationEndTime = applicationEndTime;
    }

    public String getApplicationVersionId() {
        return applicationVersionId;
    }

    public void setApplicationVersionId(String applicationVersionId) {
        this.applicationVersionId = applicationVersionId;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getVersionStatus() {
        return versionStatus;
    }

    public void setVersionStatus(String versionStatus) {
        this.versionStatus = versionStatus;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<String> getDownloadUrlList() {
        return downloadUrlList;
    }

    public void setDownloadUrlList(List<String> downloadUrlList) {
        this.downloadUrlList = downloadUrlList;
    }

    public LocalDateTime getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(LocalDateTime releaseTime) {
        this.releaseTime = releaseTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}