package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "检查版本信息")
public class CheckVersionResponseData {
    @ApiModelProperty(value = "是否有新版本", required = true)
    private boolean hasNewVersion;
    @ApiModelProperty(value = "新版本号", required = false)
    private String newVersion;
    @ApiModelProperty(value = "下载页面地址", required = false)
    private String downloadPageUrl;



    public boolean isHasNewVersion() {
        return hasNewVersion;
    }

    public void setHasNewVersion(boolean hasNewVersion) {
        this.hasNewVersion = hasNewVersion;
    }

    public String getNewVersion() {
        return newVersion;
    }

    public void setNewVersion(String newVersion) {
        this.newVersion = newVersion;
    }

    public String getDownloadPageUrl() {
        return downloadPageUrl;
    }

    public void setDownloadPageUrl(String downloadPageUrl) {
        this.downloadPageUrl = downloadPageUrl;
    }
}