package com.xingkong.wiimoo.server.application.web.dto;

import com.xingkong.wiimoo.common.support.MyPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "分页查询应用定义信息请求对象")
public class QueryApplicationPageRequest {
    @ApiModelProperty(value = "分页信息", required = true)
    @NotNull(message = "分页信息不能为空")
    @Valid
    private MyPage page;
    @ApiModelProperty(value = "应用编码", required = false)
    private String code;
    @ApiModelProperty(value = "应用名称", required = false)
    private String name;
    @ApiModelProperty(value = "应用状态", required = false)
    private List<String> statusList;

    public MyPage getPage() {
        return page;
    }

    public void setPage(MyPage page) {
        this.page = page;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }
}