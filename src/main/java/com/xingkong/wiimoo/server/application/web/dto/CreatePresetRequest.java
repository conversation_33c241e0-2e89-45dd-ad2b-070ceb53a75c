package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "创建预设请求对象")
public class CreatePresetRequest {
    @ApiModelProperty(value = "预设名称", required = true)
    @NotBlank(message = "预设名称不能为空")
    private String name;
    @ApiModelProperty(value = "预设内容", required = true)
    @NotBlank(message = "预设内容不能为空")
    private String content;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}