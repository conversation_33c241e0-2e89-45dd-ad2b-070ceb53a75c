package com.xingkong.wiimoo.server.application.db.entity;


import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Table(value = "vip_price")
public class VipPriceEntity {

  private String id;
  private String vipId;
  private BigDecimal price;
  private Integer duration;
  private String durationUnit;
  private Integer score;
  private Integer buyTimes;
  private String status;
  private LocalDateTime startTime;
  private LocalDateTime endTime;
  private LocalDateTime createdTime;
  private LocalDateTime updatedTime;

  public Integer getBuyTimes() {
    return buyTimes;
  }

  public void setBuyTimes(Integer buyTimes) {
    this.buyTimes = buyTimes;
  }

  public Integer getScore() {
    return score;
  }

  public void setScore(Integer score) {
    this.score = score;
  }

  public LocalDateTime getUpdatedTime() {
    return updatedTime;
  }

  public void setUpdatedTime(LocalDateTime updatedTime) {
    this.updatedTime = updatedTime;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public String getVipId() {
    return vipId;
  }

  public void setVipId(String vipId) {
    this.vipId = vipId;
  }


  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }


  public Integer getDuration() {
    return duration;
  }

  public void setDuration(Integer duration) {
    this.duration = duration;
  }


  public String getDurationUnit() {
    return durationUnit;
  }

  public void setDurationUnit(String durationUnit) {
    this.durationUnit = durationUnit;
  }


  public LocalDateTime getStartTime() {
    return startTime;
  }

  public void setStartTime(LocalDateTime startTime) {
    this.startTime = startTime;
  }


  public LocalDateTime getEndTime() {
    return endTime;
  }

  public void setEndTime(LocalDateTime endTime) {
    this.endTime = endTime;
  }


  public LocalDateTime getCreatedTime() {
    return createdTime;
  }

  public void setCreatedTime(LocalDateTime createdTime) {
    this.createdTime = createdTime;
  }

}