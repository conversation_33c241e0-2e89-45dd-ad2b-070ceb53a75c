package com.xingkong.wiimoo.server.application.service;

import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.application.db.entity.ResourceEntity;
import com.xingkong.wiimoo.server.application.web.dto.QueryResourceResponseData;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.xingkong.wiimoo.server.application.db.entity.table.ResourceTableDef.Resource;

@Service
@Validated
public class ResourceQueryService {
    public Result<List<QueryResourceResponseData>> queryResource(@NotBlank(message = "应用编码不能为空") String applicationCode) {

        List<QueryResourceResponseData> queryResourceResponseDataList = QueryChain.of(ResourceEntity.class).select()
                .from(ResourceEntity.class)
                .where(Resource.ApplicationCode.eq(applicationCode))
                .listAs(QueryResourceResponseData.class);
        return Result.success(queryResourceResponseDataList);
    }
}