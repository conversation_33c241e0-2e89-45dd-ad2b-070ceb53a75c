package com.xingkong.wiimoo.server.application.support;

public enum ScoreHistorySourceBizType {
    VIP("VIP","VIP赠送"),
    RECHARGE("RECHARGE","充值"),
    CONSUME("CONSUME","消费");

    private String code;
    private String desc;

    ScoreHistorySourceBizType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}