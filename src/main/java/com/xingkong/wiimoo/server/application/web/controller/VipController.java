package com.xingkong.wiimoo.server.application.web.controller;

import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.application.service.VipQueryService;
import com.xingkong.wiimoo.server.application.service.VipService;
import com.xingkong.wiimoo.server.application.web.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "VIP服务接口")
@RestController
@RequestMapping("/vip")
public class VipController {

    @Autowired
    private VipService vipService;
    @Autowired
    private VipQueryService vipQueryService;

    @ApiOperation(value = "创建vip接口",notes = "创建vip")
    @PostMapping("/create")
    @ResponseBody
    public Result createVip(@RequestBody CreateVipRequest createVipRequest) {
        return vipService.createVip(createVipRequest);
    }

    @ApiOperation(value = "更新vip基本信息接口",notes = "更新vip基本信息")
    @PostMapping("/update")
    @ResponseBody
    public Result updateVip(@RequestBody UpdateVipRequest updateVipRequest) {
        return vipService.updateVip(updateVipRequest);
    }

    @ApiOperation(value = "启用vip接口",notes = "启用vip")
    @GetMapping("/enable")
    @ResponseBody
    public Result enableVip(@ApiParam(value = "vip编码", required = true) @RequestParam("vipId") String vipId) {
        return vipService.enableVip(vipId);
    }

    @ApiOperation(value = "停用vip接口",notes = "停用vip")
    @GetMapping("/disable")
    @ResponseBody
    public Result disableVip(@ApiParam(value = "vip编码", required = true) @RequestParam("vipId") String vipId) {
        return vipService.disableVip(vipId);
    }

    @ApiOperation(value = "查询vip接口",notes = "查询vip")
    @GetMapping("/query")
    @ResponseBody
    public Result<List<QueryVipResponseData>> queryVip(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return vipQueryService.queryVip(applicationCode);
    }

    @ApiOperation(value = "创建VIP价格接口",notes = "创建VIP价格")
    @PostMapping("/price/create")
    @ResponseBody
    public Result createVipPrice(@RequestBody CreateVipPriceRequest createVipPriceRequest) {
        return vipService.createVipPrice(createVipPriceRequest);
    }

    @ApiOperation(value = "更新VIP价格接口",notes = "更新VIP价格")
    @PostMapping("/price/update")
    @ResponseBody
    public Result updateVipPrice(@RequestBody UpdateVipPriceRequest updateVipPriceRequest) {
        return vipService.updateVipPrice(updateVipPriceRequest);
    }

    @ApiOperation(value = "启用VIP价格接口",notes = "启用VIP价格")
    @GetMapping("/price/enable")
    @ResponseBody
    public Result enableVipPrice(@ApiParam(value = "vip价格编码", required = true) @RequestParam("vipPriceId") String vipPriceId) {
        return vipService.enableVipPrice(vipPriceId);
    }

    @ApiOperation(value = "停用VIP价格接口",notes = "停用VIP价格")
    @GetMapping("/price/disable")
    @ResponseBody
    public Result disableVipPrice(@ApiParam(value = "vip价格编码", required = true) @RequestParam("vipPriceId") String vipPriceId) {
        return vipService.disableVipPrice(vipPriceId);
    }

    @ApiOperation(value = "查询VIP价格接口",notes = "查询VIP价格")
    @GetMapping("/price/query")
    @ResponseBody
    public Result<List<QueryVipPriceResponseData>> queryVipPriceList(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode,
                                                                     @ApiParam(value = "vipId", required = false) @RequestParam("vipId") String vipId) {
        return vipQueryService.queryVipPriceList(applicationCode, vipId);
    }

    @ApiOperation(value = "添加VIP权限接口",   notes = "添加VIP权限"   )
    @PostMapping("/permission/add")
    @ResponseBody
    public Result addVipPermission(@RequestBody AddVipPermissionRequest addVipPermissionRequest) {
        return vipService.addVipPermission(addVipPermissionRequest);
    }

    @ApiOperation(value = "删除VIP权限接口",notes = "删除VIP权限")
    @GetMapping("/permission/delete")
    @ResponseBody
    public Result deleteVipPermission(@RequestParam ("vipPermissionId") String vipPermissionId) {
        return vipService.deleteVipPermission(vipPermissionId);
    }


    @ApiOperation(value = "查询VIP权限接口",notes = "查询VIP权限")
    @GetMapping("/permission/query")
    @ResponseBody
    public Result<List<QueryVipPermissionResponseData>> queryVipPermissionList(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode,
                                                                               @ApiParam(value = "vipId", required = false) @RequestParam("vipId") String vipId) {
        return vipQueryService.queryVipPermissionList(applicationCode, vipId);
    }

}