package com.xingkong.wiimoo.server.application.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "应用历史版本信息")
public class QueryVersionResponseData {
    @ApiModelProperty(value = "版本id", required = true)
    private String applicationVersionId;
    @ApiModelProperty(value = "版本号", required = true)
    private String version;
    @ApiModelProperty(value = "更新说明", required = true)
    private String notes;
    @ApiModelProperty(value = "下载地址", required = true)
    private List<String> downloadUrlList;
    @ApiModelProperty(value = "发布时间", required = true)
    private LocalDateTime releaseTime;
    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createdTime;

    public String getApplicationVersionId() {
        return applicationVersionId;
    }

    public void setApplicationVersionId(String applicationVersionId) {
        this.applicationVersionId = applicationVersionId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<String> getDownloadUrlList() {
        return downloadUrlList;
    }

    public void setDownloadUrlList(List<String> downloadUrlList) {
        this.downloadUrlList = downloadUrlList;
    }

    public LocalDateTime getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(LocalDateTime releaseTime) {
        this.releaseTime = releaseTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
}