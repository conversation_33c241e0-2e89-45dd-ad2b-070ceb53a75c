package com.xingkong.wiimoo.server.application.web.dto;

import com.xingkong.base.infrastructure.ability.validation.EnumValidation;
import com.xingkong.wiimoo.common.domain.time.DurationUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "创建VIP价格请求对象")
public class CreateVipPriceRequest {
    @ApiModelProperty(value = "VIP id", required = true)
    @NotBlank(message = "VIP id不能为空")
    private String vipId;
    @ApiModelProperty(value = "VIP价格", required = true)
    @NotNull(message = "VIP价格不能为空")
    @Min(value = 0, message = "VIP价格不能小于0")
    private BigDecimal price;
    @ApiModelProperty(value = "VIP时长", required = true)
    @NotNull(message = "VIP时长不能为空")
    @Min(value = 0, message = "VIP时长不能小于0")
    private Integer duration;
    @ApiModelProperty(value = "VIP时长单位", required = true)
    @NotBlank(message = "VIP时长单位不能为空")
    @EnumValidation(enumClass = DurationUnit.class, message = "不合法的VIP时长单位")
    private String durationUnit;
    @ApiModelProperty(value = "赠送积分", required = true)
    @NotNull(message = "赠送积分不能为空")
    @Min(value = 0, message = "VIP积分不能小于0")
    private Integer score;
    @ApiModelProperty(value = "VIP价格生效时间", required = true)
    @NotNull(message = "VIP价格生效时间不能为空")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "VIP价格失效时间", required = true)
    @NotNull(message = "VIP价格失效时间不能为空")
    private LocalDateTime endTime;
    @ApiModelProperty(value = "购买次数", required = true)
    @NotNull(message = "购买次数不能为空")
    @Min(value = 0, message = "购买次数不能小于0")
    private Integer buyTimes;

    public Integer getBuyTimes() {
        return buyTimes;
    }

    public void setBuyTimes(Integer buyTimes) {
        this.buyTimes = buyTimes;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getDurationUnit() {
        return durationUnit;
    }

    public void setDurationUnit(String durationUnit) {
        this.durationUnit = durationUnit;
    }
}