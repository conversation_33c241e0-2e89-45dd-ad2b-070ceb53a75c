package com.xingkong.wiimoo.server.application.service;

import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.common.support.VipPermissionStatusEnum;
import com.xingkong.wiimoo.common.support.VipPriceStatusEnum;
import com.xingkong.wiimoo.common.support.VipStatusEnum;
import com.xingkong.wiimoo.server.application.db.entity.ResourceEntity;
import com.xingkong.wiimoo.server.application.db.entity.VipEntity;
import com.xingkong.wiimoo.server.application.db.entity.VipPermissionEntity;
import com.xingkong.wiimoo.server.application.db.entity.VipPriceEntity;
import com.xingkong.wiimoo.server.application.web.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

import static com.xingkong.wiimoo.server.application.db.entity.table.ResourceTableDef.Resource;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipPermissionTableDef.VipPermission;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipPriceTableDef.VipPrice;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipTableDef.Vip;

@Service
@Validated
public class VipService {

    @Autowired
    private ApplicationValidatedService applicationValidatedService;

    public Result createVip(@Valid CreateVipRequest createVipRequest) {

        Result result = applicationValidatedService.isValid(createVipRequest.getApplicationCode());
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }


        //判断vip code 是否已存在
        VipEntity vipEntity = QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Code.eq(createVipRequest.getCode()))
                .and(Vip.ApplicationCode.eq(createVipRequest.getApplicationCode()))
                .one();
        if (vipEntity != null) {
            return Result.businessError("vip编码已存在");
        }

        vipEntity = new VipEntity();
        vipEntity.setId(UlidCreator.getUlid().toString());
        vipEntity.setApplicationCode(createVipRequest.getApplicationCode());
        vipEntity.setCode(createVipRequest.getCode());
        vipEntity.setName(createVipRequest.getName());
        vipEntity.setDescription(createVipRequest.getDescription());
        vipEntity.setStatus(VipStatusEnum.WAITING.getCode());
        vipEntity.setCreatedTime(LocalDateTime.now());
        vipEntity.setUpdatedTime(LocalDateTime.now());
        vipEntity.setStartTime(createVipRequest.getStartTime());
        vipEntity.setEndTime(createVipRequest.getEndTime());

        DbChain.table(VipEntity.class).save(vipEntity);

        return Result.success();
    }

    public Result updateVip(@Valid UpdateVipRequest updateVipRequest) {
        VipEntity vipEntity = QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Id.eq(updateVipRequest.getId()))
                .one();
        if (vipEntity == null) {
            return Result.businessError("vip不存在");
        }

        UpdateChain.of(VipEntity.class)
                .set(Vip.Name, updateVipRequest.getName())
                .set(Vip.Description, updateVipRequest.getDescription())
                .set(Vip.StartTime, updateVipRequest.getStartTime())
                .set(Vip.EndTime, updateVipRequest.getEndTime())
                .set(Vip.UpdatedTime, LocalDateTime.now())
                .where(Vip.Id.eq(updateVipRequest.getId()))
                .update();

        return Result.success();
    }

    public Result enableVip(@NotBlank(message = "vipId不能为空") String vipId) {
        VipEntity vipEntity = QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Id.eq(vipId))
                .one();
        if (vipEntity == null) {
            return Result.businessError("vip不存在");
        }
        if (VipStatusEnum.ENABLE.getCode().equals(vipEntity.getStatus())) {
            return Result.success("vip已启用");
        }

        UpdateChain.of(VipEntity.class)
                .set(Vip.Status, VipStatusEnum.ENABLE.getCode())
                .set(Vip.UpdatedTime, LocalDateTime.now())
                .where(Vip.Id.eq(vipId))
                .update();

        return Result.success();
    }

    public Result disableVip(@NotBlank(message = "vipId不能为空") String vipId) {
        VipEntity vipEntity = QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Id.eq(vipId))
                .one();
        if (vipEntity == null) {
            return Result.businessError("vip不存在");
        }
        if (VipStatusEnum.DISABLE.getCode().equals(vipEntity.getStatus())) {
            return Result.success("vip已停用");
        }

        UpdateChain.of(VipEntity.class)
                .set(Vip.Status, VipStatusEnum.DISABLE.getCode())
                .set(Vip.UpdatedTime, LocalDateTime.now())
                .where(Vip.Id.eq(vipId))
                .update();

        return Result.success();
    }


    public Result createVipPrice(@Valid CreateVipPriceRequest createVipPriceRequest) {
        // 判断vip是否存在
        VipEntity vipEntity = QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Id.eq(createVipPriceRequest.getVipId()))
                .one();
        if (vipEntity == null) {
            return Result.businessError("vip不存在");
        }

        VipPriceEntity vipPriceEntity = new VipPriceEntity();
        vipPriceEntity.setId(UlidCreator.getUlid().toString());
        vipPriceEntity.setVipId(createVipPriceRequest.getVipId());
        vipPriceEntity.setPrice(createVipPriceRequest.getPrice());
        vipPriceEntity.setDuration(createVipPriceRequest.getDuration());
        vipPriceEntity.setDurationUnit(createVipPriceRequest.getDurationUnit());
        vipPriceEntity.setCreatedTime(LocalDateTime.now());
        vipPriceEntity.setScore(createVipPriceRequest.getScore());
        vipPriceEntity.setStatus(VipPriceStatusEnum.WAITING.getCode());
        vipPriceEntity.setStartTime(createVipPriceRequest.getStartTime());
        vipPriceEntity.setEndTime(createVipPriceRequest.getEndTime());
        vipPriceEntity.setUpdatedTime(LocalDateTime.now());
        vipPriceEntity.setBuyTimes(createVipPriceRequest.getBuyTimes());

        DbChain.table(VipPriceEntity.class).save(vipPriceEntity);

        return Result.success();
    }

    public Result updateVipPrice(@Valid UpdateVipPriceRequest updateVipPriceRequest) {
        VipPriceEntity vipPriceEntity = QueryChain.of(VipPriceEntity.class).select()
                .from(VipPriceEntity.class)
                .where(VipPrice.Id.eq(updateVipPriceRequest.getId()))
                .one();
        if (vipPriceEntity == null) {
            return Result.businessError("vip价格不存在");
        }

        UpdateChain.of(VipPriceEntity.class)
                .set(VipPrice.Price, updateVipPriceRequest.getPrice())
                .set(VipPrice.Duration, updateVipPriceRequest.getDuration())
                .set(VipPrice.DurationUnit, updateVipPriceRequest.getDurationUnit())
                .set(VipPrice.StartTime, updateVipPriceRequest.getStartTime())
                .set(VipPrice.EndTime, updateVipPriceRequest.getEndTime())
                .set(VipPrice.UpdatedTime, LocalDateTime.now())
                .set(VipPrice.BuyTimes, updateVipPriceRequest.getBuyTimes())
                .where(VipPrice.Id.eq(updateVipPriceRequest.getId()))
                .update();

        return Result.success();
    }

    public Result enableVipPrice(@NotBlank(message = "vip价格id不能为空") String vipPriceId) {
        VipPriceEntity vipPriceEntity = QueryChain.of(VipPriceEntity.class).select()
                .from(VipPriceEntity.class)
                .where(VipPrice.Id.eq(vipPriceId))
                .one();
        if (vipPriceEntity == null) {
            return Result.businessError("vip价格不存在");
        }

        UpdateChain.of(VipPriceEntity.class)
                .set(VipPrice.Status, VipPriceStatusEnum.ENABLE.getCode())
                .set(VipPrice.UpdatedTime, LocalDateTime.now())
                .where(VipPrice.Id.eq(vipPriceId))
                .update();

        return Result.success();
    }

    public Result disableVipPrice(@NotBlank(message = "vip价格id不能为空") String vipPriceId) {
        VipPriceEntity vipPriceEntity = QueryChain.of(VipPriceEntity.class).select()
                .from(VipPriceEntity.class)
                .where(VipPrice.Id.eq(vipPriceId))
                .one();
        if (vipPriceEntity == null) {
            return Result.businessError("vip价格不存在");
        }

        UpdateChain.of(VipPriceEntity.class)
                .set(VipPrice.Status, VipPriceStatusEnum.DISABLE.getCode())
                .set(VipPrice.UpdatedTime, LocalDateTime.now())
                .where(VipPrice.Id.eq(vipPriceId))
                .update();

        return Result.success();

    }


    public Result addVipPermission(@Valid AddVipPermissionRequest addVipPermissionRequest) {
        VipEntity vipEntity = QueryChain.of(VipEntity.class).select()
                .from(VipEntity.class)
                .where(Vip.Id.eq(addVipPermissionRequest.getVipId()))
                .one();
        if (vipEntity == null) {
            return Result.businessError("vip不存在");
        }

        ResourceEntity resourceEntity = QueryChain.of(ResourceEntity.class).select()
                .from(ResourceEntity.class)
                .where(Resource.Id.eq(addVipPermissionRequest.getResourceId()))
                .one();
        if (resourceEntity == null) {
            return Result.businessError("资源不存在");
        }

        VipPermissionEntity vipPermissionEntity = new VipPermissionEntity();
        vipPermissionEntity.setId(UlidCreator.getUlid().toString());
        vipPermissionEntity.setVipId(addVipPermissionRequest.getVipId());
        vipPermissionEntity.setResourceId(addVipPermissionRequest.getResourceId());
        vipPermissionEntity.setStatus(VipPermissionStatusEnum.ACTIVE.getCode());
        vipPermissionEntity.setCreatedTime(LocalDateTime.now());
        vipPermissionEntity.setUpdatedTime(LocalDateTime.now());

        DbChain.table(VipPermissionEntity.class).save(vipPermissionEntity);

        return Result.success();
    }

    public Result deleteVipPermission(@NotBlank(message = "vip权限id不能为空") String vipPermissionId) {
        VipPermissionEntity vipPermissionEntity = QueryChain.of(VipPermissionEntity.class).select()
                .from(VipPermissionEntity.class)
                .where(VipPermission.Id.eq(vipPermissionId))
                .one();
        if (vipPermissionEntity == null) {
            return Result.businessError("vip权限不存在");
        }

        UpdateChain.of(VipPermissionEntity.class)
                .set(VipPermission.Status, VipPermissionStatusEnum.DELETED.getCode())
                .set(VipPermission.UpdatedTime, LocalDateTime.now())
                .where(VipPermission.Id.eq(vipPermissionId))
                .update();

        return Result.success();
    }

}