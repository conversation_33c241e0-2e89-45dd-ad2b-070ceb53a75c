package com.xingkong.wiimoo.server.application.db.dao;

import com.mybatisflex.core.BaseMapper;
import com.xingkong.wiimoo.server.application.db.entity.VipPriceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

@Mapper
public interface VipPriceDao extends BaseMapper<VipPriceEntity> {

    @Select("select * from vip_price where vip_id = #{vipId} and price = #{price}")
    VipPriceEntity selectByIdAndPrice(@Param("vipId") String vipId, @Param("price") BigDecimal price);
}