package com.xingkong.wiimoo.server.application.db.entity;

import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;

@Table(value = "application_version_download")
public class ApplicationVersionDownloadEntity {
    private String id;
    private String applicationVersionId;
    private String url;
    private LocalDateTime createdTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplicationVersionId() {
        return applicationVersionId;
    }

    public void setApplicationVersionId(String applicationVersionId) {
        this.applicationVersionId = applicationVersionId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
}