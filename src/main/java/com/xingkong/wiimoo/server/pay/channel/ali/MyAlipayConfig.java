package com.xingkong.wiimoo.server.pay.channel.ali;

import com.alipay.v3.ApiClient;
import com.alipay.v3.ApiException;
import com.alipay.v3.Configuration;
import com.alipay.v3.util.model.AlipayConfig;
import org.springframework.context.annotation.Bean;

@org.springframework.context.annotation.Configuration
public class MyAlipayConfig {
    public static final String appId = "2021000117710021";
    public static final String sellerId = "2088670571360511";
    private static final String appPrivateKey = "fdsafsda";
    private static final String alipayPublicKey = "fdsafdsafs";

    @Bean
    public ApiClient getApiClient() throws ApiException {
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setBasePath("https://openapi.alipay.com");
        // 设置alipayConfig参数（全局设置一次）
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId(appId);
        alipayConfig.setPrivateKey(appPrivateKey);
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        apiClient.setAlipayConfig(alipayConfig);
        return apiClient;
    }
}