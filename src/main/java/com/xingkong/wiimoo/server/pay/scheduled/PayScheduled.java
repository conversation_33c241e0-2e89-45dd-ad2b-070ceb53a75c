package com.xingkong.wiimoo.server.pay.scheduled;

import com.xingkong.wiimoo.server.order.db.entity.OrderEntity;
import com.xingkong.wiimoo.server.order.service.OrderQueryService;
import com.xingkong.wiimoo.server.pay.service.PayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@EnableScheduling
@ConditionalOnProperty(name = "task.pay.enabled")
public class PayScheduled {
    private static final Logger LOGGER = LoggerFactory.getLogger(PayScheduled.class);

    @Autowired
    private PayService payService;
    @Autowired
    private OrderQueryService orderQueryService;

    @Scheduled(cron = "0/5 * * * * ?")
    @Async("syncPayResultScheduler")
    public void syncPayResult() {
        LOGGER.info("========同步支付结果任务开始执行=========");
        List<OrderEntity> orderEntityList = orderQueryService.queryPayingOrder();
        LOGGER.info("待同步支付结果数:{}", orderEntityList.size());
        orderEntityList.forEach(orderEntity -> {
            LOGGER.info("开始同步支付结果任务:{}", orderEntity.getOrderNo());
            payService.syncOrderPayResult(orderEntity.getOrderNo());
            LOGGER.info("完成同步支付结果任务:{}", orderEntity.getOrderNo());
        });
        LOGGER.info("========同步支付结果任务结束执行=========");
    }
}