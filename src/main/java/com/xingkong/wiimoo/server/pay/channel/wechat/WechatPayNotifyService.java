package com.xingkong.wiimoo.server.pay.channel.wechat;

import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.wiimoo.server.order.dto.PayResultInfo;
import com.xingkong.wiimoo.server.order.support.PayMode;
import com.xingkong.wiimoo.server.pay.service.PayService;
import com.xingkong.wiimoo.server.pay.support.PayStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;

@Service
public class WechatPayNotifyService {
    private static final Logger logger = LoggerFactory.getLogger(WechatPayNotifyService.class);

    @Resource(name = "wechatPayNotificationConfig")
    private NotificationConfig notificationConfig;

    @Autowired
    private PayService payService;


    public ResponseEntity notifyPayResult(HttpServletRequest request) throws IOException {
//        HTTP 请求体 body。切记使用原始报文，不要用 JSON 对象序列化后的字符串，避免验签的 body 和原文不一致。
        String requestBody = request.getReader().lines().reduce("", String::concat);
//        HTTP 头 Wechatpay-Signature。应答的微信支付签名。
        String wechatSignature = request.getHeader("Wechatpay-Signature");
//        HTTP 头 Wechatpay-Serial。微信支付平台证书的序列号，验签必须使用序列号对应的微信支付平台证书。
        String wechatPaySerial = request.getHeader("Wechatpay-Serial");
//        HTTP 头 Wechatpay-Nonce。签名中的随机数。
        String wechatpayNonce = request.getHeader("Wechatpay-Nonce");
//        HTTP 头 Wechatpay-Timestamp。签名中的时间戳。
        String wechatTimestamp = request.getHeader("Wechatpay-Timestamp");
//        HTTP 头 Wechatpay-Signature-Type。签名类型。
        String wechatpaySignatureType = request.getHeader("Wechatpay-Signature-Type");
        // 构造 RequestParam
        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(wechatPaySerial)
                .nonce(wechatpayNonce)
                .signature(wechatSignature)
                .timestamp(wechatTimestamp)
                .body(requestBody)
                .build();

        // 初始化 NotificationParser
        NotificationParser parser = new NotificationParser(notificationConfig);
        Transaction transaction = null;
        try {
            // 以支付通知回调为例，验签、解密并转换成 Transaction
            transaction = parser.parse(requestParam, Transaction.class);
        } catch (ValidationException e) {
            // 签名验证失败，返回 401 UNAUTHORIZED 状态码
            logger.error("sign verification failed", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        PayResultInfo payResultInfo = new PayResultInfo();
        payResultInfo.setOrderNo(transaction.getOutTradeNo());
        payResultInfo.setPayMode(PayMode.ALIPAY.getCode());
        Transaction.TradeStateEnum tradeState = transaction.getTradeState();
        switch (tradeState) {
            case SUCCESS:
                payResultInfo.setPayStatus(PayStatus.SUCCESS.getCode());
                payResultInfo.setThirdPayNo(transaction.getTransactionId());
                payResultInfo.setPayTime(LocalDateTime.parse(transaction.getSuccessTime()));
                break;
            case CLOSED:
                payResultInfo.setPayStatus(PayStatus.CLOSE.getCode());
                break;
            case USERPAYING:
                payResultInfo.setPayStatus(PayStatus.PAYING.getCode());
                break;
            case PAYERROR:
                payResultInfo.setPayStatus(PayStatus.FAIL.getCode());
                break;
        }


        Result result = payService.dealPayResult(payResultInfo);
// 如果处理失败，应返回 4xx/5xx 的状态码，例如 500 INTERNAL_SERVER_ERROR
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }

// 处理成功，返回 200 OK 状态码
        return ResponseEntity.status(HttpStatus.OK).build();
    }

}