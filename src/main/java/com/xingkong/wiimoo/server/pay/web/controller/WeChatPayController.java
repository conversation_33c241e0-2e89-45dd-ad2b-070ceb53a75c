package com.xingkong.wiimoo.server.pay.web.controller;

import com.xingkong.wiimoo.server.pay.channel.wechat.WechatPayNotifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Api(tags = "微信支付服务接口")
@RestController
@RequestMapping("/wechat")
public class WeChatPayController {

    @Autowired
    private WechatPayNotifyService wechatPayNotifyService;

    @ApiOperation(value = "支付结果通知接口", notes = "支付结果通知")
    @GetMapping("/notify")
    @ResponseBody
    public ResponseEntity notifyPayResult(HttpServletRequest request) throws IOException {
        return wechatPayNotifyService.notifyPayResult(request);
    }
}