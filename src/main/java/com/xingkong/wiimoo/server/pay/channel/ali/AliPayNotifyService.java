package com.xingkong.wiimoo.server.pay.channel.ali;

import com.xingkong.wiimoo.server.pay.service.PayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class AliPayNotifyService {

    private static final Logger logger = LoggerFactory.getLogger(AliPayNotifyService.class);

    @Autowired
    private PayService payService;
    @Value("${alipayNotifyUrl}")
    private String notifyUrl;

    public String notifyPayResult(Map<String, String> params) {
        //todo 接入时在调整

//        try {
//            boolean signVerified = AlipaySignature.rsaCheckV1(params, "", params.get("charset"), params.get("sign_type"));
//        } catch (ApiException e) {
//            logger.error("支付宝支付结果通知验签失败", e);
//            return "false";
//        }
//        String orderNo = params.get("out_trade_no");
//        if (orderNo == null) {
//            logger.error("支付宝支付结果通知订单号为空");
//            return "false";
//        }
//        PayItemEntity payItemEntity = QueryChain.of(PayItemEntity.class).select()
//                .from(PayItemEntity.class)
//                .where(PayItem.PayNo.eq(payNo))
//                .one();
//        if (payItemEntity == null) {
//            logger.error("支付宝支付结果通知订单不存在");
//            return "false";
//        }
//        BigDecimal totalAmount = new BigDecimal(params.get("total_amount"));
//        if (totalAmount.compareTo(payItemEntity.getAmount()) != 0) {
//            logger.error("支付宝支付结果通知订单金额不一致");
//            return "false";
//        }
//        String sellerId = params.get("seller_id");
//        if (!sellerId.equals(MyAlipayConfig.sellerId)) {
//            logger.error("支付宝支付结果通知seller_id不一致");
//            return "false";
//        }
//        String appId = params.get("app_id");
//        if (!appId.equals(MyAlipayConfig.appId)) {
//            logger.error("支付宝支付结果通知app_id不一致");
//            return "false";
//        }
//        String tradeStatus = params.get("trade_status");
//        if (!tradeStatus.equals(AlipayStatus.TRADE_SUCCESS.getCode())) {
//            logger.error("支付宝支付结果通知trade_status不是TRADE_SUCCESS");
//            return "success";
//        }
//        LocalDateTime gmtPayment = LocalDateTime.parse(params.get("gmt_payment"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//
//        PayResultNotifyRequest payResultNotifyRequest = new PayResultNotifyRequest();
//        payResultNotifyRequest.setOrderNo(payNo);
//        payResultNotifyRequest.setPayStatus(PayStatus.SUCCESS.getCode());
//        payResultNotifyRequest.setThirdPayNo(UlidCreator.getUlid().toString());
//        payResultNotifyRequest.setPayTime(gmtPayment);
//        payResultNotifyRequest.setAmount(totalAmount);
//        payResultNotifyRequest.setPayMode(PayMode.ALIPAY.getCode());
//
//        PayResultInfo payResultInfo = new PayResultInfo();
//        payResultInfo.setOrderNo(payNo);
//        payResultInfo.setPayStatus(PayStatus.SUCCESS.getCode());
//        payResultInfo.setThirdPayNo();
//        payResultInfo.setPayTime(gmtPayment);
//        payResultInfo.setAmount(totalAmount);
//        payResultInfo.setPayMode(PayMode.ALIPAY.getCode());
//
//
//        payNotifyService.notify(payResultNotifyRequest);
        return "success";
    }
}