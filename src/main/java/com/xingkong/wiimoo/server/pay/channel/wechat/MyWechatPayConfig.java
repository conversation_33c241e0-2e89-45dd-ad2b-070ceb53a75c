package com.xingkong.wiimoo.server.pay.channel.wechat;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyWechatPayConfig {
    public static final String appId = "wx4772ff164352a4b2";
    /**
     * 商户号
     */
    public static String merchantId = "1722575130";
    /**
     * 商户API私钥路径
     */
    public static String privateKeyPath = "/Users/<USER>/your/path/apiclient_key.pem";
    public static String privateKeyStr = "-----BEGIN PRIVATE KEY-----\n" +
            "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC4A6z8s6odktPy\n" +
            "qcAmvVCEA09afDa+2N9xK2nF78VZqyexQRoLNrneLA4olJzoFax51/ETOukBOKst\n" +
            "xPhOwOdlAeSW6iZisKouF6FKBGZxyWWiyO2Y1rX8PRZ9KSmo53cKLUDHLthS8Qbb\n" +
            "e38rbqNUCrj3Q9p+31xeUXuF1+Bbh6BTTuPK/uG/wbmG6W4opHKnBmIKjyEJv5xO\n" +
            "W8aytmb8Vps+ggijB8oayvvE4VeZlTjrqAv94RBBjA9AjS7tvlikZRRadn/ambe+\n" +
            "3hPcvhcsFSgkBPGDmIGmSVx7gTrYuz4oqWw2WnT/NRH7SDvHet69pYYIVRrRX3V8\n" +
            "R4CZboIdAgMBAAECggEBAJjYVMPB6DeQUgRqA3UqIsrEhRE/w6laMp6p1L3YnzAP\n" +
            "/nQ5Awz1dAWaylfiuUH9XroGJ/Q1Jn0hOO5XgXjIumsn21uPOjsXSwPmFM6KQR2a\n" +
            "j5VbGB+vmJHfm+JyYmwtdq85T2ki/ARIt81vEv5cc8XG8kRQVBi2Jt8hB1zxJyZe\n" +
            "ZH2VxtxHlSoJJPwBQSExuZ4hjeH/xwqGwu/0zOF1iSpPw5ZqaXSi29xp9gThc6xw\n" +
            "3UyI/49UM732Mno+6jMNBAfZzTiK0RpS2Df9QyzgELMoT7Y/W49TfM5p8djJN6AQ\n" +
            "T9+B9uUqf2plQoHqDuTJ697E+CmXzuPyeB/2OG1lxXECgYEA4PhF7bKPuQOMgbLW\n" +
            "O9ta3SZ3c01hQmw6DNdhUZfZ0Qq5RJG/cSs8miGrZ0KqPTkWBFxTRhjVKZE6DGP0\n" +
            "qiRnEbel+VUrxp1pUXuPgMTrGEOJGhROiafD4Drq5mQ4j5BpRJ/eF4R+v79FVJo8\n" +
            "uTUJYOvYshZkICQBAbbhtVTUvNMCgYEA0WVB8B9m0at+JztMtycue5mZir3KyWeG\n" +
            "g0zOHaQ/FkYSbbobSVmZtkADhXcZst6VxTq1Rf8jeczPXbxnSbqw10bmPxILrg9T\n" +
            "8MFtorcLdY20ILqPI3LxyX8zpWNSmXGN7QtttzGUNFB8r9uYs8S4tQbcunGrMBVB\n" +
            "k66bPAX8r08CgYBofQfFogS88tgEuXJldLlcM+yRAiSlPC0uH68UvYmuXOO15Ffd\n" +
            "kS5hjDfXCxCUxPnj5pnK2efPmaMzdA2EJMo2YWzWRGfDpQLwlIoPTea/a0a3dS0R\n" +
            "JHmGoPbPMUdP7svl0rbNh31zQ7xI0iZYAzkq6f/Inzu77d0F1dI5bYAtfwKBgDOr\n" +
            "tPW4Cfs7QkoUC/42IPZHFOhzpRLGBR9pWINgAGFSsUF6ZzlWbuPWMAhDN4g3/OVK\n" +
            "c2juaKapr5IF58b91p2I1VBC97bf+WqzkIZSfPVpHkXlQ2UP/DIovWnWdI+AwfD4\n" +
            "nP5TsaqnId2TZr8VkC67nsQzQHcBB8mVkKDjKVNpAoGBAJDvpCaCSv2eHXQWEdT0\n" +
            "l0zLOn3x/5T0TT6Mxqs/0mEmYJrXOXuE2ibWXDUazmPprNvCfoz/F82tXTw5UchB\n" +
            "roSYV1+ecEAVMOHVD4ts1dWztff3ADpddXi2Ahnnmv+TTXpr4NV/cwab3YjyBx7E\n" +
            "AxW8ONeHknPsaF1uKMO2zblq\n" +
            "-----END PRIVATE KEY-----\n"   ;
    /**
     * 商户证书序列号
     */
    public static String merchantSerialNumber = "77D41C0B022B1A7641A929E409B4A259CFAA00C3";
    /**
     * 商户APIV3密钥
     */
    public static String apiV3key = "283c9ba75210451faa2d3740e979c727";

    @Bean("wechatPayConfig")
    public Config getWechatPayConfig() {

        return new RSAAutoCertificateConfig.Builder()
                .merchantId(merchantId)
//                .privateKeyFromPath(privateKeyPath)
                .privateKey(privateKeyStr)
                .merchantSerialNumber(merchantSerialNumber)
                .apiV3Key(apiV3key)
                .build();
    }
    @Bean("wechatPayNotificationConfig")
    public NotificationConfig getWechatPayNotificationConfig() {
        // 2. 如果你仍在使用微信支付平台证书，则使用 RSAAutoCertificateConfig
        return  new RSAAutoCertificateConfig.Builder()
                .merchantId(merchantId)
                .privateKey(privateKeyStr)
                .merchantSerialNumber(merchantSerialNumber)
                .apiV3Key(apiV3key)
                .build();
    }
}