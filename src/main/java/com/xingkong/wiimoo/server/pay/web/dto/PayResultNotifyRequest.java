package com.xingkong.wiimoo.server.pay.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xingkong.base.infrastructure.ability.validation.Condition;
import com.xingkong.base.infrastructure.ability.validation.ConditionalValidation;
import com.xingkong.base.infrastructure.ability.validation.EnumValidation;
import com.xingkong.wiimoo.server.order.support.PayMode;
import com.xingkong.wiimoo.server.pay.support.PayStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "支付结果通知请求对象")
@ConditionalValidation
public class PayResultNotifyRequest {
    @ApiModelProperty(value = "订单号", required = true)
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    @ApiModelProperty(value = "支付状态", required = true)
    @NotBlank(message = "支付状态不能为空")
    @EnumValidation(enumClass = PayStatus.class, message = "不合法的支付状态")
    private String payStatus;
    @ApiModelProperty(value = "第三方支付单号", required = true)
    @Condition(when = "payMode!= T(com.xingkong.wiimoo.server.order.support.PayMode).NONE.getCode()&& payStatus== T(com.xingkong.wiimoo.server.pay.support.PayStatus).SUCCESS.getCode()", then = "thirdPayNo!=null", message = "第三方支付单号不能为空")
    private String thirdPayNo;
    @ApiModelProperty(value = "支付时间", required = true)
    @Condition(when = "payMode!= T(com.xingkong.wiimoo.server.order.support.PayMode).NONE.getCode()&&payStatus== T(com.xingkong.wiimoo.server.pay.support.PayStatus).SUCCESS.getCode()", then = "payTime!=null", message = "支付时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    @ApiModelProperty(value = "支付方式", required = true)
    @NotBlank(message = "支付方式不能为空")
    @EnumValidation(enumClass = PayMode.class, message = "不合法的支付方式")
    private String payMode;
    @ApiModelProperty(value = "支付金额", required = true)
    @Condition(when = "payMode!= T(com.xingkong.wiimoo.server.order.support.PayMode).NONE.getCode()&&payStatus== T(com.xingkong.wiimoo.server.pay.support.PayStatus).SUCCESS.getCode()", then = "amount!=null", message = "支付金额不能为空")
    private BigDecimal amount;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getThirdPayNo() {
        return thirdPayNo;
    }

    public void setThirdPayNo(String thirdPayNo) {
        this.thirdPayNo = thirdPayNo;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }
}