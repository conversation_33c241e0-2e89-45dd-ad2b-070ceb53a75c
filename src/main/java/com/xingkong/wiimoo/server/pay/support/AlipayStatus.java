package com.xingkong.wiimoo.server.pay.support;

import java.util.Map;

public enum AlipayStatus {
    WAIT_BUYER_PAY("WAIT_BUYER_PAY","等待支付"),
    TRADE_SUCCESS("TRADE_SUCCESS","支付成功"),
    TRADE_CLOSED("TRADE_CLOSED","已关闭"),
    TRADE_FINISHED("TRADE_FINISHED","交易结束");
    private String code;
    private String desc;

    AlipayStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, AlipayStatus> map = new java.util.HashMap<String, AlipayStatus>();
    static {
        for (AlipayStatus alipayStatus : AlipayStatus.values()) {
            map.put(alipayStatus.getCode(), alipayStatus);
        }
    }
    public static AlipayStatus getAlipayStatus(String code) {
        return map.get(code);
    }
}