package com.xingkong.wiimoo.server.pay.channel.ali;

import com.alipay.v3.ApiException;
import com.alipay.v3.api.AlipayTradeApi;
import com.alipay.v3.model.AlipayTradeQueryModel;
import com.alipay.v3.model.AlipayTradeQueryResponseModel;
import com.alipay.v3.util.GenericExecuteApi;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.order.dto.PayInfo;
import com.xingkong.wiimoo.server.order.dto.PayResultInfo;
import com.xingkong.wiimoo.server.order.support.PayMode;
import com.xingkong.wiimoo.server.pay.support.AlipayStatus;
import com.xingkong.wiimoo.server.pay.support.PayStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Service
public class AliPayService {

    private static final Logger logger = LoggerFactory.getLogger(AliPayService.class);

    @Value("${alipayNotifyUrl}")
    private String notifyUrl;


    public String getPayUrl(PayInfo payInfo) throws ApiException {

        GenericExecuteApi alipayApi = new GenericExecuteApi();

        // 构造请求参数以调用接口
        Map<String, Object> bizParams = new HashMap<>();
        Map<String, Object> bizContent = new HashMap<>();
        // 设置商户订单号
        bizContent.put("out_trade_no", payInfo.getOrderNo());
        // 设置订单总金额
        bizContent.put("total_amount", payInfo.getTotalAmount().toPlainString());
        // 设置订单标题
        bizContent.put("subject", payInfo.getSkuName());
        // 设置产品码
        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
        // 设置跳转链接
        bizContent.put("return_url", payInfo.getReturnUrl());
        String expireTime = payInfo.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        bizContent.put("timeout_expire", expireTime);
        bizContent.put("notify_url", notifyUrl);
        bizParams.put("biz_content", bizContent);

        return alipayApi.pageExecute("alipay.trade.page.pay", "GET", bizParams);

    }

    public Result<PayResultInfo> syncPayResult(String orderNo) {
        AlipayTradeQueryModel alipayTradeQueryModel = new AlipayTradeQueryModel();
        alipayTradeQueryModel.setOutTradeNo(orderNo);

        AlipayTradeApi alipayApi = new AlipayTradeApi();
        PayResultInfo payResultInfo = new PayResultInfo();
        try {
            AlipayTradeQueryResponseModel alipayTradeQueryResponseModel = alipayApi.query(alipayTradeQueryModel);
            payResultInfo.setOrderNo(alipayTradeQueryResponseModel.getOutTradeNo());
            AlipayStatus alipayStatus = AlipayStatus.getAlipayStatus(alipayTradeQueryResponseModel.getAdditionalStatus());
            switch (alipayStatus) {
                case TRADE_SUCCESS:
                    payResultInfo.setPayStatus(PayStatus.SUCCESS.getCode());
                    break;
                case TRADE_CLOSED:
                    payResultInfo.setPayStatus(PayStatus.CLOSE.getCode());
                    break;
                case WAIT_BUYER_PAY:
                    payResultInfo.setPayStatus(PayStatus.PAYING.getCode());
                    break;
                case TRADE_FINISHED:
                    payResultInfo.setPayStatus(PayStatus.SUCCESS.getCode());
                    break;
                default:
                    payResultInfo.setPayStatus(PayStatus.FAIL.getCode());
                    break;
            }
            payResultInfo.setPayResultMsg(AlipayStatus.getAlipayStatus(alipayTradeQueryResponseModel.getAdditionalStatus()).getDesc());
            payResultInfo.setThirdPayNo(alipayTradeQueryResponseModel.getTradeNo());
            payResultInfo.setPayTime(LocalDateTime.parse(alipayTradeQueryResponseModel.getSendPayDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            payResultInfo.setAmount(new BigDecimal(alipayTradeQueryResponseModel.getTotalAmount()));
            payResultInfo.setPayMode(PayMode.ALIPAY.getCode());
            return Result.success(payResultInfo);
        } catch (ApiException e) {
            logger.error("同步支付宝支付结果失败", e);
            return Result.businessError(e.getMessage());
        }
    }

    public void close(String orderNo) {
        //todo 还未对接支付宝，暂不编码
        return;
    }
}