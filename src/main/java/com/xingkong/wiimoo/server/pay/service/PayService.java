package com.xingkong.wiimoo.server.pay.service;

import com.alipay.v3.ApiException;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.base.infrastructure.ability.lock.distributed.DistributedLock;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.support.ResultCode;
import com.xingkong.base.infrastructure.ability.util.DLockUtil;
import com.xingkong.wiimoo.server.application.db.entity.ScorePriceEntity;
import com.xingkong.wiimoo.server.application.db.entity.VipPriceEntity;
import com.xingkong.wiimoo.server.application.support.ScoreHistorySourceBizType;
import com.xingkong.wiimoo.server.application.support.VipHistoryBizType;
import com.xingkong.wiimoo.server.consumer.service.ConsumerService;
import com.xingkong.wiimoo.server.order.db.entity.OrderEntity;
import com.xingkong.wiimoo.server.order.db.entity.OrderItemEntity;
import com.xingkong.wiimoo.server.order.dto.PayInfo;
import com.xingkong.wiimoo.server.order.dto.PayResultInfo;
import com.xingkong.wiimoo.server.order.support.OrderStatus;
import com.xingkong.wiimoo.server.order.support.PayMode;
import com.xingkong.wiimoo.server.order.support.SkuType;
import com.xingkong.wiimoo.server.order.web.dto.pay.GetPayUrlResponseData;
import com.xingkong.wiimoo.server.order.web.dto.pay.SyncPayResultResponse;
import com.xingkong.wiimoo.server.pay.channel.ali.AliPayService;
import com.xingkong.wiimoo.server.pay.channel.wechat.WechatPayService;
import com.xingkong.wiimoo.server.pay.db.PayItemDao;
import com.xingkong.wiimoo.server.pay.db.PayItemEntity;
import com.xingkong.wiimoo.server.pay.support.PayStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.xingkong.wiimoo.server.application.db.entity.table.ScorePriceTableDef.ScorePrice;
import static com.xingkong.wiimoo.server.application.db.entity.table.VipPriceTableDef.VipPrice;
import static com.xingkong.wiimoo.server.order.db.entity.table.OrderItemTableDef.OrderItem;
import static com.xingkong.wiimoo.server.order.db.entity.table.OrderTableDef.Order;
import static com.xingkong.wiimoo.server.pay.db.table.PayItemTableDef.PayItem;

@Service
@Validated
public class PayService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PayService.class);

    @Autowired
    private AliPayService aliPayService;
    @Autowired
    private WechatPayService wechatPayService;
    @Autowired
    private PayItemDao payItemDao;
    @Autowired
    private ConsumerService consumerService;

    public Result<GetPayUrlResponseData> getPayUrl(PayInfo payInfo) {
        OrderEntity orderEntity = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(payInfo.getOrderNo()))
                .one();
        if (orderEntity == null) {
            return Result.businessError("订单不存在");
        }
        if (OrderStatus.PAID.getCode().equals(orderEntity.getOrderStatus())) {
            GetPayUrlResponseData getPayUrlResponseData = new GetPayUrlResponseData();
            getPayUrlResponseData.setCanPay(false);
            getPayUrlResponseData.setOrderStatus(OrderStatus.PAID.getCode());
            return Result.success(getPayUrlResponseData);
        }
        if (OrderStatus.CLOSED.getCode().equals(orderEntity.getOrderStatus())) {
            GetPayUrlResponseData getPayUrlResponseData = new GetPayUrlResponseData();
            getPayUrlResponseData.setCanPay(false);
            getPayUrlResponseData.setOrderStatus(OrderStatus.CLOSED.getCode());
            return Result.success(getPayUrlResponseData);
        }

        PayItemEntity payItemEntity = QueryChain.of(PayItemEntity.class).select()
                .from(PayItemEntity.class)
                .where(PayItem.OrderNo.eq(payInfo.getOrderNo()))
                .and(PayItem.PayMode.eq(payInfo.getPayMode()))
                .one();
        if (payItemEntity != null) {
            GetPayUrlResponseData getPayUrlResponseData = new GetPayUrlResponseData();
            getPayUrlResponseData.setPayUrl(payItemEntity.getThirdPayUrl());
            getPayUrlResponseData.setPayNo(payItemEntity.getPayNo());
            getPayUrlResponseData.setCanPay(true);
            getPayUrlResponseData.setOrderStatus(OrderStatus.WAIT_PAY.getCode());
            return Result.success(getPayUrlResponseData);
        }


        //todo 根据支付方式生成支付链接
        String payUrl = "";
        PayMode payMode = PayMode.getPayMode(payInfo.getPayMode());
        switch (payMode) {
            case ALIPAY:
                try {
                    payUrl = aliPayService.getPayUrl(payInfo);
                } catch (ApiException e) {
                    return Result.businessError(e.getMessage());
                }
                break;
            case WECHAT:
                payUrl = wechatPayService.getPayUrl(payInfo);
                break;
            case NONE:
                break;
        }

        payItemEntity = new PayItemEntity();
        payItemEntity.setOrderNo(payInfo.getOrderNo());
        payItemEntity.setPayNo(payInfo.getPayNo());
        payItemEntity.setPayMode(payInfo.getPayMode());
        payItemEntity.setAmount(payInfo.getTotalAmount());
        payItemEntity.setStatus(PayStatus.PAYING.getCode());
        payItemEntity.setCreatedTime(LocalDateTime.now());
        payItemEntity.setUpdatedTime(payItemEntity.getCreatedTime());
        payItemEntity.setThirdPayUrl(payUrl);
        payItemEntity.setThirdPayUrlTimeExpire(payInfo.getExpireTime());
        payItemDao.insert(payItemEntity);

        GetPayUrlResponseData getPayUrlResponseData = new GetPayUrlResponseData();
        getPayUrlResponseData.setPayUrl(payUrl);
        getPayUrlResponseData.setPayNo(payItemEntity.getPayNo());
        getPayUrlResponseData.setCanPay(true);
        getPayUrlResponseData.setOrderStatus(OrderStatus.WAIT_PAY.getCode());
        return Result.success(getPayUrlResponseData);
    }

    public Result<SyncPayResultResponse> syncOrderPayResult(@NotBlank(message = "订单号不能为空") String orderNo) {
        SyncPayResultResponse syncPayResultResponse = new SyncPayResultResponse();
        DistributedLock distributedLock = DLockUtil.getLock();
        //对将要处理的订单加锁
        boolean isLock = distributedLock.tryLock(orderNo, 5, 1000 * 60, TimeUnit.MICROSECONDS);
        if (!isLock) {
            LOGGER.info("获取锁失败:{}", orderNo);
            return Result.businessError("该订单正在处理中，请稍后再试");
        }
        //判断订单状态及相关处理
        OrderEntity orderEntity = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(orderNo))
                .one();
        if (orderEntity == null) {
            distributedLock.unlock();
            return Result.businessError("订单不存在");
        }
        if (OrderStatus.PAID.getCode().equals(orderEntity.getOrderStatus())) {
            distributedLock.unlock();
            syncPayResultResponse.setPayResultMsg("订单已支付");
            syncPayResultResponse.setPayStatus(PayStatus.SUCCESS.getCode());
            syncPayResultResponse.setOrderStatus(OrderStatus.PAID.getCode());
            return Result.success(syncPayResultResponse);
        }
        if (OrderStatus.CLOSED.getCode().equals(orderEntity.getOrderStatus())) {
            distributedLock.unlock();
            syncPayResultResponse.setPayResultMsg("订单已关闭");
            syncPayResultResponse.setPayStatus(PayStatus.CLOSE.getCode());
            syncPayResultResponse.setOrderStatus(OrderStatus.CLOSED.getCode());
            return Result.success(syncPayResultResponse);
        }

        //同步所有支付明细的支付结果
        List<PayItemEntity> payItemEntityList = QueryChain.of(PayItemEntity.class).select()
                .from(PayItemEntity.class)
                .where(PayItem.OrderNo.eq(orderNo))
                .list();
        for (PayItemEntity payItemEntity : payItemEntityList) {
            //从数据库获取最细状态
            payItemEntity = QueryChain.of(PayItemEntity.class).select()
                    .from(PayItemEntity.class)
                    .where(PayItem.PayNo.eq(payItemEntity.getPayNo()))
                    .one();
            if (PayStatus.CLOSE.getCode().equals(payItemEntity.getStatus()) ||
                    PayStatus.SUCCESS.getCode().equals(payItemEntity.getStatus())) {
                continue;
            }
            Result<PayResultInfo> payItemResult = syncPayItemResult(payItemEntity);
            if (!ResultCode.SUCCESS.getCode().equals(payItemResult.getResultCode())) {
                continue;
            }
            dealPayResult(payItemResult.getData());
        }

        //再次查询订单状态，如果在上面的处理过程中，订单已经支付成功，则接下来把其他支付明细置为关闭
        orderEntity = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(orderNo))
                .one();
        if (OrderStatus.PAID.getCode().equals(orderEntity.getOrderStatus())) {
            payItemEntityList = QueryChain.of(PayItemEntity.class).select()
                    .from(PayItemEntity.class)
                    .where(PayItem.OrderNo.eq(orderNo))
                    .and(PayItem.Status.ne(PayStatus.SUCCESS.getCode()))
                    .list();
            for (PayItemEntity payItemEntity : payItemEntityList) {
                payItemClose(payItemEntity);
            }
        }
        distributedLock.unlock();

        syncPayResultResponse.setOrderStatus(orderEntity.getOrderStatus());
        syncPayResultResponse.setPayResultMsg(OrderStatus.getOrderStatus(orderEntity.getOrderStatus()).getDesc());
        return Result.success(syncPayResultResponse);
    }

    private Result<PayResultInfo> syncPayItemResult(PayItemEntity payItemEntity) {
        Result<PayResultInfo> payResultInfoResult = null;
        PayMode payMode = PayMode.getPayMode(payItemEntity.getPayMode());
        switch (payMode) {
            case ALIPAY:
                payResultInfoResult = aliPayService.syncPayResult(payItemEntity.getOrderNo());
                break;
            case WECHAT:
                payResultInfoResult = wechatPayService.syncPayResult(payItemEntity.getOrderNo());
                break;
            case NONE:
                return Result.businessError("不支持的支付方式");
        }
        if (ResultCode.SUCCESS.getCode().equals(payResultInfoResult.getResultCode())) {
            payResultInfoResult.getData().setPayNo(payItemEntity.getPayNo());
        }
        return payResultInfoResult;
    }

    public Result dealPayResult(PayResultInfo payResultInfo) {
        Result result = checkPayResult(payResultInfo);
        if (!ResultCode.SUCCESS.getCode().equals(result.getResultCode())) {
            return result;
        }
        PayStatus status = PayStatus.getPayStatus(payResultInfo.getPayStatus());
        switch (status) {
            case SUCCESS:
                paySuccess(payResultInfo);
                break;
            case FAIL:
                payFail(payResultInfo);
                break;
            case CLOSE:
                payClose(payResultInfo);
                break;
            case PAYING:
                paying(payResultInfo);
                break;
        }
        return Result.success();
    }

    private Result checkPayResult(PayResultInfo payResultInfo) {
        OrderEntity orderEntity = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(payResultInfo.getOrderNo()))
                .one();
        if (orderEntity == null) {
            return Result.businessError("订单不存在");
        }
        PayItemEntity payItemEntity = QueryChain.of(PayItemEntity.class).select()
                .from(PayItemEntity.class)
                .where(PayItem.OrderNo.eq(payResultInfo.getOrderNo()))
                .and(PayItem.PayMode.eq(payResultInfo.getPayMode()))
                .one();
        if (payItemEntity == null) {
            return Result.businessError("支付记录不存在");
        }
        if (PayStatus.SUCCESS.getCode().equals(payResultInfo.getPayStatus())) {
            if (payItemEntity.getAmount().compareTo(payResultInfo.getAmount()) != 0) {
                return Result.businessError("支付金额与订单金额不一致");
            }
            if (payItemEntity.getStatus().equals(PayStatus.SUCCESS.getCode())) {
                return Result.success();
            }
            // 处理无支付方式的支付结果
            if (PayMode.NONE.getCode().equals(payResultInfo.getPayMode())) {
                if (orderEntity.getTotalAmount().compareTo(BigDecimal.ZERO) != 0) {
                    return Result.businessError("订单金额不为0");
                }
            }
        }
        return Result.success();
    }

    private void paying(PayResultInfo payResultInfo) {
        UpdateChain.of(PayItemEntity.class)
                .set(PayItem.Status, PayStatus.PAYING.getCode())
                .set(PayItem.UpdatedTime, LocalDateTime.now())
                .where(PayItem.PayNo.eq(payResultInfo.getPayNo()))
                .update();
    }

    private Result paySuccess(PayResultInfo payResultInfo) {

        UpdateChain.of(OrderEntity.class)
                .set(Order.OrderStatus, OrderStatus.PAID.getCode())
                .set(Order.UpdatedTime, LocalDateTime.now())
                .where(Order.OrderNo.eq(payResultInfo.getOrderNo()))
                .update();
        UpdateChain.of(PayItemEntity.class)
                .set(PayItem.Status, PayStatus.SUCCESS.getCode())
                .set(PayItem.UpdatedTime, LocalDateTime.now())
                .where(PayItem.PayNo.eq(payResultInfo.getPayNo()))
                .update();
        OrderEntity orderEntity = QueryChain.of(OrderEntity.class).select()
                .from(OrderEntity.class)
                .where(Order.OrderNo.eq(payResultInfo.getOrderNo()))
                .one();
        List<OrderItemEntity> orderItemEntityList = QueryChain.of(OrderItemEntity.class).select()
                .from(OrderItemEntity.class)
                .where(OrderItem.OrderNo.eq(payResultInfo.getOrderNo()))
                .list();
        orderItemEntityList.forEach(orderItemEntity -> {
            if (SkuType.VIP.getCode().equals(orderItemEntity.getSkuType())) {
                VipPriceEntity vipPriceEntity = QueryChain.of(VipPriceEntity.class).select()
                        .from(VipPriceEntity.class)
                        .where(VipPrice.Id.eq(orderItemEntity.getSkuId()))
                        .one();
                consumerService.addVip(payResultInfo.getOrderNo(), VipHistoryBizType.BUY, orderEntity.getConsumerId(), vipPriceEntity.getId(), vipPriceEntity.getDuration() * orderItemEntity.getSkuQuantity(), vipPriceEntity.getDurationUnit());
            }
            if (SkuType.SCORE.getCode().equals(orderItemEntity.getSkuType())) {
                ScorePriceEntity scorePriceEntity = QueryChain.of(ScorePriceEntity.class).select()
                        .from(ScorePriceEntity.class)
                        .where(ScorePrice.Id.eq(orderItemEntity.getSkuId()))
                        .one();
                consumerService.addScore(orderEntity.getConsumerId(), orderItemEntity.getSkuQuantity() * scorePriceEntity.getUnitQuantity(), ScoreHistorySourceBizType.RECHARGE, payResultInfo.getOrderNo());
            }
        });
        return Result.success();
    }

    private void payFail(PayResultInfo payResultInfo) {
        UpdateChain.of(PayItemEntity.class)
                .set(PayItem.Status, PayStatus.FAIL.getCode())
                .set(PayItem.UpdatedTime, LocalDateTime.now())
                .where(PayItem.PayNo.eq(payResultInfo.getPayNo()))
                .update();
    }

    private void payClose(PayResultInfo payResultInfo) {
        UpdateChain.of(PayItemEntity.class)
                .set(PayItem.Status, PayStatus.CLOSE.getCode())
                .set(PayItem.UpdatedTime, LocalDateTime.now())
                .where(PayItem.PayNo.eq(payResultInfo.getPayNo()))
                .update();
    }

    private void payItemClose(PayItemEntity payItemEntity) {
        UpdateChain.of(PayItemEntity.class)
                .set(PayItem.Status, PayStatus.CLOSE.getCode())
                .set(PayItem.UpdatedTime, LocalDateTime.now())
                .where(PayItem.PayNo.eq(payItemEntity.getPayNo()))
                .update();
        PayMode payMode = PayMode.getPayMode(payItemEntity.getPayMode());
        switch (payMode) {
            case ALIPAY:
                aliPayService.close(payItemEntity.getOrderNo());
                break;
            case WECHAT:
                wechatPayService.close(payItemEntity.getOrderNo());
                break;
            case NONE:
                break;
        }
    }


}