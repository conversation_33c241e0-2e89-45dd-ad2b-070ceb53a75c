package com.xingkong.wiimoo.server.pay.channel;

import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.order.dto.PayInfo;
import com.xingkong.wiimoo.server.order.dto.PayResultInfo;

public interface IPayChannelService {
    String getPayUrl(PayInfo payInfo);

    String notifyPayResult(Object params);

    Result<PayResultInfo> syncPayResult(String payNo);
}