package com.xingkong.wiimoo.server.pay.db;

import com.mybatisflex.annotation.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Table(value = "pay_item")
public class PayItemEntity {
    private String orderNo;
    private String payNo;
    private String thirdPayNo;
    private String payMode;
    private BigDecimal amount;
    private String status;
    private LocalDateTime createdTime;
    private LocalDateTime payTime;
    private LocalDateTime updatedTime;
    private String thirdPayUrl;
    private LocalDateTime thirdPayUrlTimeExpire;

    public String getThirdPayUrl() {
        return thirdPayUrl;
    }

    public void setThirdPayUrl(String thirdPayUrl) {
        this.thirdPayUrl = thirdPayUrl;
    }

    public LocalDateTime getThirdPayUrlTimeExpire() {
        return thirdPayUrlTimeExpire;
    }

    public void setThirdPayUrlTimeExpire(LocalDateTime thirdPayUrlTimeExpire) {
        this.thirdPayUrlTimeExpire = thirdPayUrlTimeExpire;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public String getThirdPayNo() {
        return thirdPayNo;
    }

    public void setThirdPayNo(String thirdPayNo) {
        this.thirdPayNo = thirdPayNo;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}