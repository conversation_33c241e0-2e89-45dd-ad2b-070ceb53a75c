package com.xingkong.wiimoo.server.pay.channel.wechat;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.*;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.base.infrastructure.ability.util.JsonUtils;
import com.xingkong.wiimoo.server.order.dto.PayInfo;
import com.xingkong.wiimoo.server.order.dto.PayResultInfo;
import com.xingkong.wiimoo.server.order.support.PayMode;
import com.xingkong.wiimoo.server.pay.support.PayStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class WechatPayService {
    private static final Logger logger = LoggerFactory.getLogger(WechatPayService.class);

    @Resource(name = "wechatPayConfig")
    private Config config;
    @Resource(name = "wechatPayNotificationConfig")
    private NotificationConfig notificationConfig;
    @Value("${wechatNotifyUrl}")
    private String notifyUrl;

    public String getPayUrl(PayInfo payInfo) {
        NativePayService service = new NativePayService.Builder().config(config).build();
        // request.setXxx(val)设置所需参数，具体参数可见Request定义
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        amount.setTotal(payInfo.getTotalAmount().intValue() * 100);
        request.setAmount(amount);
        request.setAppid(MyWechatPayConfig.appId);
        request.setMchid(MyWechatPayConfig.merchantId);
        request.setDescription(payInfo.getSkuName());
        request.setOutTradeNo(payInfo.getOrderNo());
        request.setTimeExpire(payInfo.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
        request.setNotifyUrl(notifyUrl);
        logger.info("微信支付请求参数:{}", JsonUtils.toJson(request));
        // 调用下单方法，得到应答
        PrepayResponse response = service.prepay(request);
        // 使用微信扫描 code_url 对应的二维码，即可体验Native支付
        return response.getCodeUrl();
    }


    public Result<PayResultInfo> syncPayResult(String orderNo) {
        NativePayService service = new NativePayService.Builder().config(config).build();
        QueryOrderByOutTradeNoRequest queryRequest = new QueryOrderByOutTradeNoRequest();
        queryRequest.setMchid(MyWechatPayConfig.merchantId);
        queryRequest.setOutTradeNo(orderNo);
        Transaction result = null;
        try {
            result = service.queryOrderByOutTradeNo(queryRequest);
        } catch (ServiceException e) {
            // API返回失败, 例如ORDER_NOT_EXISTS
            logger.error("code={}, message={}\n", e.getErrorCode(), e.getErrorMessage());
            logger.error("reponse body={}\n", e.getResponseBody());
            PayResultInfo payResultInfo = new PayResultInfo();
            payResultInfo.setPayResultMsg(e.getErrorMessage());
            payResultInfo.setPayStatus(PayStatus.FAIL.getCode());
            return Result.success(payResultInfo);
        }
        logger.info("微信支付结果:{}", JsonUtils.toJson(result));
        PayResultInfo payResultInfo = new PayResultInfo();
        payResultInfo.setPayResultMsg(result.getTradeStateDesc());
        payResultInfo.setAmount(new BigDecimal(result.getAmount().getTotal()).divide(new BigDecimal(100)));
        payResultInfo.setOrderNo(orderNo);
        payResultInfo.setPayMode(PayMode.WECHAT.getCode());
        Transaction.TradeStateEnum tradeState = result.getTradeState();
        switch (tradeState) {
            case SUCCESS:
                payResultInfo.setPayStatus(PayStatus.SUCCESS.getCode());
                payResultInfo.setThirdPayNo(result.getTransactionId());
                payResultInfo.setPayTime(OffsetDateTime.parse(result.getSuccessTime()).toLocalDateTime());
                break;
            case CLOSED:
                payResultInfo.setPayStatus(PayStatus.CLOSE.getCode());
                break;
            case USERPAYING:
            case NOTPAY:
            case REVOKED:
                payResultInfo.setPayStatus(PayStatus.PAYING.getCode());
                break;
            case PAYERROR:
                payResultInfo.setPayStatus(PayStatus.FAIL.getCode());
                break;

        }
        return Result.success(payResultInfo);
    }

    public void close(String orderNo) {

        CloseOrderRequest closeRequest = new CloseOrderRequest();
        closeRequest.setMchid(MyWechatPayConfig.merchantId);
        closeRequest.setOutTradeNo(orderNo);
// 方法没有返回值，意味着成功时API返回204 No Content
        NativePayService service = new NativePayService.Builder().config(config).build();
        service.closeOrder(closeRequest);
    }
}