package com.xingkong.wiimoo.server.pay.support;

import java.util.Map;

public enum PayStatus {
    PAYING("PAYING","支付中"),
    SUCCESS("SUCCESS","支付成功"),
    FAIL("FAIL","支付失败"),
    CLOSE("CLOSE","已关闭");
    private String code;
    private String desc;

    PayStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, PayStatus> map = new java.util.HashMap<String, PayStatus>();
    static {
        for (PayStatus payStatus : PayStatus.values()) {
            map.put(payStatus.getCode(), payStatus);
        }
    }
    public static PayStatus getPayStatus(String code) {
        return map.get(code);
    }
}