package com.xingkong.wiimoo.server.pay.web.controller;

import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.order.web.dto.pay.SyncPayResultResponse;
import com.xingkong.wiimoo.server.pay.service.PayService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "支付服务接口")
@RestController
@RequestMapping("/pay")
public class PayController {

    @Autowired
    private PayService payService;

    @GetMapping("/sync/result")
    @ResponseBody
    public Result<SyncPayResultResponse> syncPayResult(@RequestParam("orderNo") String orderNo ) {
        return payService.syncOrderPayResult(orderNo);
    }
}