package com.xingkong.wiimoo.server.pay.web.controller;

import com.xingkong.wiimoo.server.pay.channel.ali.AliPayNotifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(tags = "支付宝服务接口")
@RestController
@RequestMapping("/alipay")
public class AliPayController {

    @Autowired
    private AliPayNotifyService aliPayNotifyService;

    @ApiOperation(value = "支付结果通知接口", notes = "支付结果通知")
    @PostMapping("/notify")
    @ResponseBody
    public String notifyPayResult(@RequestParam Map<String, String> params) {
        return aliPayNotifyService.notifyPayResult(params);
    }
}