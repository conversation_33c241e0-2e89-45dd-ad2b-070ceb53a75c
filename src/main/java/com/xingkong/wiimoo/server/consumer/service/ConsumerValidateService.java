package com.xingkong.wiimoo.server.consumer.service;

import com.mybatisflex.core.query.QueryChain;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerEntity;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;

import static com.xingkong.wiimoo.server.consumer.db.entity.table.ConsumerTableDef.Consumer;

@Service
public class ConsumerValidateService {


    public Result isValid(@NotBlank(message = "用户id不能为空") String consumerId) {
        ConsumerEntity consumerEntity =  QueryChain.of(ConsumerEntity.class).select()
                .from(ConsumerEntity.class)
                .where(Consumer.Id.eq(consumerId))
                .one();
        if (consumerEntity==null) {
            return Result.businessError("用户不存在");
        }
        return Result.success();
    }
}