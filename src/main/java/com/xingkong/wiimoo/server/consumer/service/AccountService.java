package com.xingkong.wiimoo.server.consumer.service;

import cn.dev33.satoken.stp.StpLogic;
import com.mybatisflex.core.paginate.Page;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.consumer.db.dao.ConsumerScoreDao;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerScoreEntity;
import com.xingkong.wiimoo.server.consumer.web.dto.QueryMyOrderPageRequest;
import com.xingkong.wiimoo.server.consumer.web.dto.QueryMyScoreResponseData;
import com.xingkong.wiimoo.server.order.service.OrderQueryService;
import com.xingkong.wiimoo.server.order.web.dto.query.QueryOrderPageRequest;
import com.xingkong.wiimoo.server.order.web.dto.query.QueryOrderResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.util.UriUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.nio.charset.StandardCharsets;

@Service
@Validated
public class AccountService {

    @Autowired
    private ConsumerScoreDao consumerScoreDao;

    @Autowired
    private OrderQueryService orderQueryService;

    @Autowired
    private ConsumerService consumerService;

    public String myVipInfo(@Valid @NotBlank(message = "应用编码不能为空") String applicationCode) {

        return null;
    }

    public Result<QueryMyScoreResponseData> myScore() {
        ConsumerScoreEntity consumerScoreEntity = consumerScoreDao.selectAllByConsumerId(StpKit.STORE.getLoginIdAsString());

        QueryMyScoreResponseData queryMyScoreResponseData = new QueryMyScoreResponseData();
        if (consumerScoreEntity == null) {
            queryMyScoreResponseData.setScore(0);
        } else {
            queryMyScoreResponseData.setScore(consumerScoreEntity.getScore());
        }
        return Result.success(queryMyScoreResponseData);
    }

    public Result<Page<QueryOrderResponseData>> myOrder(QueryMyOrderPageRequest queryMyOrderPageRequest) {
        QueryOrderPageRequest orderQueryRequest = new QueryOrderPageRequest();
        orderQueryRequest.setConsumerId(StpKit.STORE.getLoginIdAsString());
        orderQueryRequest.setPage(queryMyOrderPageRequest.getPage());
        return orderQueryService.queryOrder(orderQueryRequest);
    }

    public Result<QueryOrderResponseData> myOrder(String orderNo) {
        return orderQueryService.queryOrder(orderNo);
    }

    @Value("${storeBaseUrl}")
    private String storeBaseUrl;

    public Result<String> queryMyHomeUrl(@Valid @NotBlank(message = "应用编码不能为空") String applicationCode) {
        StpLogic stpLogic = StpKit.getStpLogic(applicationCode);
        if (!stpLogic.isLogin()) {
            return Result.authenticationError("未登录");
        }
        String url = storeBaseUrl + "/login";
        String consumerCenterUrl = storeBaseUrl + "/consumer/center";
        String consumerId = StpKit.getStpLogic(applicationCode).getLoginIdAsString();
        StpKit.STORE.login(consumerId);
        String token = StpKit.STORE.getTokenValue();
        url += "?token=" + token;
        url += "&redirectUrl=" + UriUtils.encode(consumerCenterUrl, StandardCharsets.UTF_8);

        return Result.success(url);
    }

}