package com.xingkong.wiimoo.server.consumer.service;

import com.github.f4b6a3.ulid.UlidCreator;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import com.xingkong.wiimoo.common.domain.time.DurationModel;
import com.xingkong.wiimoo.server.application.db.entity.VipPriceEntity;
import com.xingkong.wiimoo.server.application.support.ScoreHistorySourceBizType;
import com.xingkong.wiimoo.server.application.support.VipHistoryBizType;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerScoreEntity;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerScoreHistoryEntity;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerVipEntity;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerVipHistoryEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static com.xingkong.wiimoo.server.application.db.entity.table.VipPriceTableDef.VipPrice;
import static com.xingkong.wiimoo.server.consumer.db.entity.table.ConsumerScoreTableDef.ConsumerScore;
import static com.xingkong.wiimoo.server.consumer.db.entity.table.ConsumerVipTableDef.ConsumerVip;

@Service
public class ConsumerService {

    @Transactional
    public void addVip(String sourceBizNo,VipHistoryBizType vipHistoryBizType, String consumerId, String vipPriceId, int duration, String durationUnit) {
        VipPriceEntity vipPriceEntity =   QueryChain.of(VipPriceEntity.class).select()
                .from(VipPriceEntity.class)
                .where(VipPrice.Id.eq(vipPriceId))
                .one();
        ConsumerVipEntity consumerVipEntity =   QueryChain.of(ConsumerVipEntity.class).select()
                .from(ConsumerVipEntity.class)
                .where(ConsumerVip.ConsumerId.eq(consumerId))
                .and(ConsumerVip.VipId.eq(vipPriceEntity.getVipId()))
                .one();
        if (consumerVipEntity != null) {
            renewVip(sourceBizNo,vipHistoryBizType, consumerVipEntity, duration, durationUnit);
        } else {
            addNewVip(sourceBizNo, vipHistoryBizType,consumerId, vipPriceEntity.getVipId(), duration, durationUnit);
        }

        addScore(consumerId, vipPriceEntity.getScore(), ScoreHistorySourceBizType.VIP, sourceBizNo);

    }


    private void addNewVip(String sourceBizNo, VipHistoryBizType vipHistoryBizType, String consumerId, String vipId, int duration, String durationUnit) {
        ConsumerVipEntity consumerVipEntity = new ConsumerVipEntity();
        consumerVipEntity.setId(UlidCreator.getUlid().toString());
        consumerVipEntity.setConsumerId(consumerId);
        consumerVipEntity.setVipId(vipId);
        consumerVipEntity.setStartTime(LocalDateTime.now());
        consumerVipEntity.setEndTime(LocalDateTime.now().plusSeconds(DurationModel.create(duration, durationUnit).getSeconds()));
        consumerVipEntity.setCreatedTime(LocalDateTime.now());
        consumerVipEntity.setUpdatedTime(consumerVipEntity.getCreatedTime());

        DbChain.table(ConsumerVipEntity.class).save(consumerVipEntity);

        ConsumerVipHistoryEntity consumerVipHistoryEntity = new ConsumerVipHistoryEntity();
        consumerVipHistoryEntity.setId(UlidCreator.getUlid().toString());
        consumerVipHistoryEntity.setConsumerVipId(consumerVipEntity.getId());
        consumerVipHistoryEntity.setOldEndTime(null);
        consumerVipHistoryEntity.setNewEndTime(consumerVipEntity.getEndTime());
        consumerVipHistoryEntity.setSourceBizType(vipHistoryBizType.getCode());
        consumerVipHistoryEntity.setSourceBizNo(sourceBizNo);
        consumerVipHistoryEntity.setCreatedTime(LocalDateTime.now());

        DbChain.table(ConsumerVipHistoryEntity.class).save(consumerVipHistoryEntity);
    }

    private void renewVip(String sourceBizNo,VipHistoryBizType vipHistoryBizType, ConsumerVipEntity consumerVipEntity, int duration, String durationUnit) {
        LocalDateTime oldEndTime = consumerVipEntity.getEndTime();

        UpdateChain.of(ConsumerVipEntity.class)
                .set(ConsumerVip.EndTime, oldEndTime.plusSeconds(DurationModel.create(duration, durationUnit).getSeconds()))
                .set(ConsumerVip.UpdatedTime, LocalDateTime.now())
                .where(ConsumerVip.Id.eq(consumerVipEntity.getId()))
                .update();

        ConsumerVipHistoryEntity consumerVipHistoryEntity = new ConsumerVipHistoryEntity();
        consumerVipHistoryEntity.setId(UlidCreator.getUlid().toString());
        consumerVipHistoryEntity.setConsumerVipId(consumerVipEntity.getId());
        consumerVipHistoryEntity.setOldEndTime(oldEndTime);
        consumerVipHistoryEntity.setNewEndTime(consumerVipEntity.getEndTime());
        consumerVipHistoryEntity.setSourceBizType(vipHistoryBizType.getCode());
        consumerVipHistoryEntity.setSourceBizNo(sourceBizNo);
        consumerVipHistoryEntity.setCreatedTime(LocalDateTime.now());

        DbChain.table(ConsumerVipHistoryEntity.class).save(consumerVipHistoryEntity);

    }

    @Transactional
    public void addScore(String consumerId, Integer score, ScoreHistorySourceBizType scoreHistorySourceBizType, String sourceBizNo) {
        ConsumerScoreEntity consumerScoreEntity =  QueryChain.of(ConsumerScoreEntity.class).select()
                .from(ConsumerScoreEntity.class)
                .where(ConsumerScore.ConsumerId.eq(consumerId))
                .one();
        Integer oldScore = consumerScoreEntity.getScore();

        UpdateChain.of(ConsumerScoreEntity.class)
                .set(ConsumerScore.Score, oldScore + score)
                .set(ConsumerScore.UpdatedTime, LocalDateTime.now())
                .where(ConsumerScore.Id.eq(consumerScoreEntity.getId()))
                .update();

        ConsumerScoreHistoryEntity consumerScoreHistoryEntity = new ConsumerScoreHistoryEntity();
        consumerScoreHistoryEntity.setId(UlidCreator.getUlid().toString());
        consumerScoreHistoryEntity.setConsumerScoreId(consumerScoreEntity.getId());
        consumerScoreHistoryEntity.setOldScore(oldScore);
        consumerScoreHistoryEntity.setNewScore(consumerScoreEntity.getScore());
        consumerScoreHistoryEntity.setSourceBizType(scoreHistorySourceBizType.getCode());
        consumerScoreHistoryEntity.setSourceBizNo(sourceBizNo);
        consumerScoreHistoryEntity.setCreatedTime(LocalDateTime.now());
        DbChain.table(ConsumerScoreHistoryEntity.class).save(consumerScoreHistoryEntity);
    }
}