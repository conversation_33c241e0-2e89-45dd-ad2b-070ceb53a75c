package com.xingkong.wiimoo.server.consumer.web.dto;

import com.xingkong.wiimoo.common.support.MyPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@ApiModel(description = "查询我的订单请求对象")
public class QueryMyOrderPageRequest {
    @ApiModelProperty(value = "分页信息", required = true)
    @NotNull(message = "分页信息不能为空")
    @Valid
    private MyPage page;

    public MyPage getPage() {
        return page;
    }

    public void setPage(MyPage page) {
        this.page = page;
    }

}