package com.xingkong.wiimoo.server.consumer.db.entity;


import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;

@Table(value = "consumer_score")
public class ConsumerScoreEntity {

  private String id;
  private String consumerId;
  private Integer score;
  private LocalDateTime createdTime;
  private LocalDateTime updatedTime;


  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public String getConsumerId() {
    return consumerId;
  }

  public void setConsumerId(String consumerId) {
    this.consumerId = consumerId;
  }


  public Integer getScore() {
    return score;
  }

  public void setScore(Integer score) {
    this.score = score;
  }


  public LocalDateTime getCreatedTime() {
    return createdTime;
  }

  public void setCreatedTime(LocalDateTime createdTime) {
    this.createdTime = createdTime;
  }


  public LocalDateTime getUpdatedTime() {
    return updatedTime;
  }

  public void setUpdatedTime(LocalDateTime updatedTime) {
    this.updatedTime = updatedTime;
  }

}