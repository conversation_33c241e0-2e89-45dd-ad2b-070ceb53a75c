package com.xingkong.wiimoo.server.consumer.db.entity;


import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;
@Table(value = "consumer")
public class ConsumerEntity {

  private String id;
  private LocalDateTime createdTime;


  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public LocalDateTime getCreatedTime() {
    return createdTime;
  }

  public void setCreatedTime(LocalDateTime createdTime) {
    this.createdTime = createdTime;
  }

}