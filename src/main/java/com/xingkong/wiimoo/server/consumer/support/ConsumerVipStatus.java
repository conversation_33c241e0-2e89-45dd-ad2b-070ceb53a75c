package com.xingkong.wiimoo.server.consumer.support;

import java.util.HashMap;
import java.util.Map;

public enum ConsumerVipStatus {
    ACTIVE("ACTIVE", "激活"),
    CLOSE("CLOSE", "关停"),
    EXPIRED("EXPIRED", "已过期");
    private String code;
    private String name;

    ConsumerVipStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    private static Map<String, ConsumerVipStatus> map = new HashMap<String, ConsumerVipStatus>();

    static {
        for (ConsumerVipStatus consumerVipStatus : ConsumerVipStatus.values()) {
            map.put(consumerVipStatus.getCode(), consumerVipStatus);
        }
    }

    public static ConsumerVipStatus getConsumerVipStatus(String code) {
        return map.get(code);
    }
}