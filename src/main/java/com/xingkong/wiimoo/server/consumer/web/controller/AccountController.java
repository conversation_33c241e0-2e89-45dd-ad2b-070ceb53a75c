package com.xingkong.wiimoo.server.consumer.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.mybatisflex.core.paginate.Page;
import com.xingkong.base.infrastructure.ability.support.Result;
import com.xingkong.wiimoo.common.config.StpKit;
import com.xingkong.wiimoo.server.application.service.VipQueryService;
import com.xingkong.wiimoo.server.application.web.dto.QueryMyVipResponseData;
import com.xingkong.wiimoo.server.consumer.service.AccountService;
import com.xingkong.wiimoo.server.consumer.web.dto.QueryMyOrderPageRequest;
import com.xingkong.wiimoo.server.consumer.web.dto.QueryMyScoreResponseData;
import com.xingkong.wiimoo.server.order.web.dto.query.QueryOrderResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "账户服务接口"    )
@RestController
@RequestMapping("/account")
public class AccountController {

    @Autowired
    private AccountService accountService;
    @Autowired
    private VipQueryService vipQueryService;

    @ApiOperation(value = "查询我的积分接口",notes = "查询积分信息")
    @GetMapping("/query/my/score")
    @ResponseBody
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    public Result<QueryMyScoreResponseData> myScore() {
        return accountService.myScore();
    }

    @ApiOperation(value = "查询我的订单接口",nickname = "我的订单",notes = "查询订单信息")
    @PostMapping("/query/my/order")
    @ResponseBody
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    public Result<Page<QueryOrderResponseData>> myOrder(@RequestBody QueryMyOrderPageRequest queryMyOrderPageRequest) {
        return accountService.myOrder(queryMyOrderPageRequest);
    }

    @ApiOperation(value = "查询我的订单接口",nickname = "我的订单",notes = "查询订单信息")
    @GetMapping("/query/my/order")
    @ResponseBody
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    public Result<QueryOrderResponseData> myOrder(@RequestParam("orderNo") String orderNo) {
        return accountService.myOrder(orderNo);
    }

    @ApiOperation(value = "获取个人中心地址",notes = "获取个人中心地址")
    @GetMapping("/get/my/homeUrl")
    @ResponseBody
    public Result<String> queryMyHomeUrl(@ApiParam(value = "应用编码", required = true) @RequestParam("applicationCode") String applicationCode) {
        return accountService.queryMyHomeUrl(applicationCode);
    }

    @ApiOperation(value = "查询我的所有VIP信息接口",notes = "查询VIP信息")
    @GetMapping("/query/my/vips")
    @ResponseBody
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    public Result<List<QueryMyVipResponseData>> myVipInfos() {
        return vipQueryService.myVipInfos();
    }

    @ApiOperation(    value = "查询指定应用我的VIP信息接口",notes = "查询VIP信息"   )
    @GetMapping("/query/my/vip/{applicationCode}")
    @ResponseBody
    @SaCheckLogin(type = StpKit.STORE_TYPE)
    public String myVipInfo(@ApiParam(value = "应用编码", required = true) @PathVariable("applicationCode") String applicationCode) {
        return vipQueryService.myVipInfo(applicationCode);
    }
}