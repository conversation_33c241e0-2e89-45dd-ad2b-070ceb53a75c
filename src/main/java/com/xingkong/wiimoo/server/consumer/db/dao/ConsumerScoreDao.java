package com.xingkong.wiimoo.server.consumer.db.dao;

import com.mybatisflex.core.BaseMapper;
import com.xingkong.wiimoo.server.consumer.db.entity.ConsumerScoreEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ConsumerScoreDao extends BaseMapper<ConsumerScoreEntity> {
    @Select("select * from consumer_score where consumer_id = #{consumerId}")
    ConsumerScoreEntity selectAllByConsumerId(String loginIdAsString);
}