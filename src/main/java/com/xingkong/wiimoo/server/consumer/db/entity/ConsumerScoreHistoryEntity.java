package com.xingkong.wiimoo.server.consumer.db.entity;


import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;

@Table(value = "consumer_score_history")
public class ConsumerScoreHistoryEntity {

  private String id;
  private String consumerScoreId;
  private Integer oldScore;
  private Integer newScore;
  private String sourceBizType;
  private String sourceBizNo;
  private LocalDateTime createdTime;


  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public String getConsumerScoreId() {
    return consumerScoreId;
  }

  public void setConsumerScoreId(String consumerScoreId) {
    this.consumerScoreId = consumerScoreId;
  }


  public Integer getOldScore() {
    return oldScore;
  }

  public void setOldScore(Integer oldScore) {
    this.oldScore = oldScore;
  }


  public Integer getNewScore() {
    return newScore;
  }

  public void setNewScore(Integer newScore) {
    this.newScore = newScore;
  }


  public String getSourceBizType() {
    return sourceBizType;
  }

  public void setSourceBizType(String sourceBizType) {
    this.sourceBizType = sourceBizType;
  }


  public String getSourceBizNo() {
    return sourceBizNo;
  }

  public void setSourceBizNo(String sourceBizNo) {
    this.sourceBizNo = sourceBizNo;
  }


  public LocalDateTime getCreatedTime() {
    return createdTime;
  }

  public void setCreatedTime(LocalDateTime createdTime) {
    this.createdTime = createdTime;
  }

}