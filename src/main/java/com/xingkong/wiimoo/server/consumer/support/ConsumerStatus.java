package com.xingkong.wiimoo.server.consumer.support;

import java.util.HashMap;
import java.util.Map;

public enum ConsumerStatus {
    DELETED("DELETED", "已删除"),
    NORMAL("NORMAL", "正常"),
    FORBIDDEN("FORBIDDEN", "禁用");

    private String code;
    private String name;

    ConsumerStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    private static Map<String, ConsumerStatus> map = new HashMap<String, ConsumerStatus>();

    static {
        for (ConsumerStatus consumerStatus : ConsumerStatus.values()) {
            map.put(consumerStatus.getCode(), consumerStatus);
        }
    }

    public static ConsumerStatus getConsumerStatus(String code) {
        return map.get(code);
    }
}