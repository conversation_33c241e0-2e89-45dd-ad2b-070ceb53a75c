package com.xingkong.wiimoo.server.consumer.db.entity;


import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;

@Table(value = "consumer_vip_history")
public class ConsumerVipHistoryEntity {

  private String id;
  private String consumerVipId;
  private LocalDateTime oldEndTime;
  private LocalDateTime newEndTime;
  private LocalDateTime createdTime;
  private String sourceBizType;
  private String sourceBizNo;


  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public String getConsumerVipId() {
    return consumerVipId;
  }

  public void setConsumerVipId(String consumerVipId) {
    this.consumerVipId = consumerVipId;
  }


  public LocalDateTime getOldEndTime() {
    return oldEndTime;
  }

  public void setOldEndTime(LocalDateTime oldEndTime) {
    this.oldEndTime = oldEndTime;
  }


  public LocalDateTime getNewEndTime() {
    return newEndTime;
  }

  public void setNewEndTime(LocalDateTime newEndTime) {
    this.newEndTime = newEndTime;
  }



  public LocalDateTime getCreatedTime() {
    return createdTime;
  }

  public void setCreatedTime(LocalDateTime createdTime) {
    this.createdTime = createdTime;
  }


  public String getSourceBizType() {
    return sourceBizType;
  }

  public void setSourceBizType(String sourceBizType) {
    this.sourceBizType = sourceBizType;
  }


  public String getSourceBizNo() {
    return sourceBizNo;
  }

  public void setSourceBizNo(String sourceBizNo) {
    this.sourceBizNo = sourceBizNo;
  }

}