<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xingkong.wiimoo</groupId>
    <artifactId>wiimoo-admin-server</artifactId>
    <version>1.0-SNAPSHOT</version>

    <parent>
        <groupId>com.xingkong.base</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.xingkong.base</groupId>
            <artifactId>xingkong-application-starter</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <!-- sa-token 权限认证, 在线文档：https://sa-token.cc/ -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
        </dependency>
        <!-- sa-token整合redis (使用jackson序列化方式) -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-template</artifactId>
        </dependency>
        <dependency>
            <groupId>org.casbin</groupId>
            <artifactId>casdoor-java-sdk</artifactId>
            <version>1.35.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alipay.sdk/alipay-easysdk -->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java-v3</artifactId>
            <version>3.1.38.ALL</version>
        </dependency>
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>0.2.17</version>
        </dependency>
        <!-- Google 国际手机号格式库 -->
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>8.13.26</version> <!-- 使用最新版本 -->
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>